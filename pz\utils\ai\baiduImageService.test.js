// 百度AI图像识别服务测试文件
// 用于验证API集成的正确性

import {
  getBaiduAccessToken,
  callDishDetectionAPI,
  callIngredientDetectionAPI,
  callImageUnderstandingAPI,
  comprehensiveFoodRecognition
} from './baiduImageService.js'

/**
 * 测试百度Access Token获取
 */
export async function testBaiduAccessToken() {
  try {
    console.log('🧪 测试百度Access Token获取...')
    const token = await getBaiduAccessToken()
    
    if (token && typeof token === 'string' && token.length > 0) {
      console.log('✅ Access Token获取成功:', token.substring(0, 20) + '...')
      return { success: true, token: token }
    } else {
      throw new Error('Token格式不正确')
    }
  } catch (error) {
    console.error('❌ Access Token获取失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试菜品识别API
 */
export async function testDishDetection(imagePath) {
  try {
    console.log('🧪 测试菜品识别API...')
    const result = await callDishDetectionAPI(imagePath)
    
    if (result.success && Array.isArray(result.data)) {
      console.log('✅ 菜品识别测试成功:', result.data.length, '个结果')
      result.data.forEach((dish, index) => {
        console.log(`  ${index + 1}. ${dish.name} (置信度: ${Math.round(dish.confidence * 100)}%)`)
      })
      return { success: true, results: result.data }
    } else {
      console.log('⚠️ 菜品识别无结果:', result.message || result.error)
      return { success: false, message: result.message || result.error }
    }
  } catch (error) {
    console.error('❌ 菜品识别测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试果蔬识别API
 */
export async function testIngredientDetection(imagePath) {
  try {
    console.log('🧪 测试果蔬识别API...')
    const result = await callIngredientDetectionAPI(imagePath)
    
    if (result.success && Array.isArray(result.data)) {
      console.log('✅ 果蔬识别测试成功:', result.data.length, '个结果')
      result.data.forEach((ingredient, index) => {
        console.log(`  ${index + 1}. ${ingredient.name} (置信度: ${Math.round(ingredient.confidence * 100)}%)`)
      })
      return { success: true, results: result.data }
    } else {
      console.log('⚠️ 果蔬识别无结果:', result.message || result.error)
      return { success: false, message: result.message || result.error }
    }
  } catch (error) {
    console.error('❌ 果蔬识别测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试图像内容理解API
 */
export async function testImageUnderstanding(imagePath) {
  try {
    console.log('🧪 测试图像内容理解API...')
    const result = await callImageUnderstandingAPI(imagePath)
    
    if (result.success && Array.isArray(result.data)) {
      console.log('✅ 图像理解测试成功:', result.data.length, '个结果')
      
      // 提取关键词和标签
      const allKeywords = []
      const allTags = []
      
      result.data.forEach(item => {
        if (item.keyword && Array.isArray(item.keyword)) {
          allKeywords.push(...item.keyword)
        }
        if (item.tag && Array.isArray(item.tag)) {
          allTags.push(...item.tag)
        }
      })
      
      console.log('  关键词:', allKeywords.slice(0, 10).join('、'))
      console.log('  标签:', allTags.slice(0, 8).join('、'))
      
      return { 
        success: true, 
        results: result.data,
        keywords: allKeywords,
        tags: allTags
      }
    } else {
      console.log('⚠️ 图像理解无结果:', result.message || result.error)
      return { success: false, message: result.message || result.error }
    }
  } catch (error) {
    console.error('❌ 图像理解测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试综合食物识别
 */
export async function testComprehensiveRecognition(imagePath) {
  try {
    console.log('🧪 测试综合食物识别...')
    const result = await comprehensiveFoodRecognition(imagePath)
    
    if (result.success) {
      const data = result.data
      console.log('✅ 综合识别测试成功:')
      console.log(`  菜品识别: ${data.dishResults.length} 个结果`)
      console.log(`  果蔬识别: ${data.ingredientResults.length} 个结果`)
      console.log(`  关键词: ${data.allKeywords.length} 个`)
      console.log(`  标签: ${data.allTags.length} 个`)
      console.log('  识别摘要:')
      console.log(data.summary)
      
      return { success: true, data: data }
    } else {
      console.log('⚠️ 综合识别失败:', result.error)
      return { success: false, error: result.error }
    }
  } catch (error) {
    console.error('❌ 综合识别测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests(imagePath) {
  console.log('🚀 开始运行百度AI服务测试套件...')
  
  const results = {
    accessToken: null,
    dishDetection: null,
    ingredientDetection: null,
    imageUnderstanding: null,
    comprehensiveRecognition: null
  }
  
  // 测试Access Token
  results.accessToken = await testBaiduAccessToken()
  
  if (!results.accessToken.success) {
    console.error('❌ Access Token测试失败，跳过其他测试')
    return results
  }
  
  if (!imagePath) {
    console.log('⚠️ 未提供测试图片路径，跳过图像识别测试')
    return results
  }
  
  // 测试各个API
  results.dishDetection = await testDishDetection(imagePath)
  results.ingredientDetection = await testIngredientDetection(imagePath)
  results.imageUnderstanding = await testImageUnderstanding(imagePath)
  results.comprehensiveRecognition = await testComprehensiveRecognition(imagePath)
  
  // 生成测试报告
  console.log('\n📊 测试报告:')
  console.log('=====================================')
  console.log(`Access Token: ${results.accessToken.success ? '✅ 通过' : '❌ 失败'}`)
  console.log(`菜品识别: ${results.dishDetection?.success ? '✅ 通过' : '❌ 失败'}`)
  console.log(`果蔬识别: ${results.ingredientDetection?.success ? '✅ 通过' : '❌ 失败'}`)
  console.log(`图像理解: ${results.imageUnderstanding?.success ? '✅ 通过' : '❌ 失败'}`)
  console.log(`综合识别: ${results.comprehensiveRecognition?.success ? '✅ 通过' : '❌ 失败'}`)
  console.log('=====================================')
  
  return results
}

/**
 * 性能测试
 */
export async function performanceTest(imagePath, iterations = 3) {
  console.log(`🏃 开始性能测试 (${iterations} 次迭代)...`)
  
  const times = []
  
  for (let i = 0; i < iterations; i++) {
    const startTime = Date.now()
    
    try {
      await comprehensiveFoodRecognition(imagePath)
      const endTime = Date.now()
      const duration = endTime - startTime
      times.push(duration)
      
      console.log(`  第 ${i + 1} 次: ${duration}ms`)
    } catch (error) {
      console.error(`  第 ${i + 1} 次失败:`, error.message)
    }
  }
  
  if (times.length > 0) {
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length
    const minTime = Math.min(...times)
    const maxTime = Math.max(...times)
    
    console.log('\n📈 性能统计:')
    console.log(`  平均耗时: ${avgTime.toFixed(2)}ms`)
    console.log(`  最短耗时: ${minTime}ms`)
    console.log(`  最长耗时: ${maxTime}ms`)
    
    return {
      average: avgTime,
      min: minTime,
      max: maxTime,
      times: times
    }
  } else {
    console.log('❌ 性能测试失败，无有效数据')
    return null
  }
}

// 导出测试工具函数
export const testUtils = {
  testBaiduAccessToken,
  testDishDetection,
  testIngredientDetection,
  testImageUnderstanding,
  testComprehensiveRecognition,
  runAllTests,
  performanceTest
}
