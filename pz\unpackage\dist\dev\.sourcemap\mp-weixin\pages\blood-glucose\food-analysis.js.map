{"version": 3, "file": "food-analysis.js", "sources": ["pages/blood-glucose/food-analysis.vue", "D:/bc Files/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYmxvb2QtZ2x1Y29zZS9mb29kLWFuYWx5c2lzLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"page-container\">\n\t\t<navbar :isHomePage=\"false\" title=\"饮食分析\" />\n\n\t\t<!-- 主体内容 -->\n\t\t<view class=\"content-container\" :style=\"contentStyle\">\n\n\t\t\t<!-- 上传区域 -->\n\t\t\t<view class=\"upload-section\">\n\t\t\t\t<view class=\"upload-header\">\n\t\t\t\t\t<text class=\"upload-title\">AI食物识别</text>\n\t\t\t\t\t<text class=\"upload-subtitle\">拍照或选择图片，AI将分析食物营养成分</text>\n\t\t\t\t</view>\n\n\n\t\t\t\t<view class=\"upload-area\" @tap=\"chooseImage\">\n\t\t\t\t\t<image v-if=\"selectedImage\" :src=\"selectedImage\" class=\"preview-image\" mode=\"aspectFit\"></image>\n\t\t\t\t\t<view v-else class=\"upload-placeholder\">\n\t\t\t\t\t\t<image src=\"../../static/resource/images/camera.png\" class=\"upload-icon\"></image>\n\t\t\t\t\t\t<text class=\"upload-text\">点击上传食物图片</text>\n\t\t\t\t\t\t<text class=\"upload-hint\">支持JPG、PNG格式</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"upload-actions\" v-if=\"selectedImage\">\n\t\t\t\t\t<button class=\"action-btn secondary\" @tap=\"reSelectImage\">重新选择</button>\n\t\t\t\t\t<button class=\"action-btn primary\" @tap=\"analyzeFood\" :disabled=\"analyzing\">\n\t\t\t\t\t\t{{ analyzing ? '分析中...' : '开始分析' }}\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 分析状态提示 -->\n\t\t\t\t<view class=\"analysis-status\" v-if=\"analyzing || errorMessage\">\n\t\t\t\t\t<view class=\"status-item\" v-if=\"analyzing\">\n\t\t\t\t\t\t<view class=\"loading-icon\"></view>\n\t\t\t\t\t\t<text class=\"status-text\">{{ currentStep }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"error-item\" v-if=\"errorMessage\">\n\t\t\t\t\t\t<text class=\"error-icon\">⚠️</text>\n\t\t\t\t\t\t<text class=\"error-text\">{{ errorMessage }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 分析结果 -->\n\t\t\t<view class=\"analysis-result\" v-if=\"analysisResult\">\n\t\t\t\t<view class=\"result-header\">\n\t\t\t\t\t<text class=\"result-title\">分析结果</text>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 识别的食物 -->\n\t\t\t\t<view class=\"food-info\">\n\t\t\t\t\t<view class=\"food-name\">\n\t\t\t\t\t\t<text class=\"name-text\">{{ analysisResult.foodName }}</text>\n\t\t\t\t\t\t<view class=\"confidence-badge\">\n\t\t\t\t\t\t\t<text class=\"confidence-text\">置信度: {{ analysisResult.confidence }}%</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 营养成分 -->\n\t\t\t\t<view class=\"nutrition-section\">\n\t\t\t\t\t<view class=\"section-title\">营养成分 (每100g)</view>\n\t\t\t\t\t<view class=\"nutrition-grid\">\n\t\t\t\t\t\t<view class=\"nutrition-item\">\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">热量</text>\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.calories }}</text>\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">kcal</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"nutrition-item\">\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">碳水化合物</text>\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.carbs }}</text>\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">g</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"nutrition-item\">\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">蛋白质</text>\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.protein }}</text>\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">g</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"nutrition-item\">\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">脂肪</text>\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.fat }}</text>\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">g</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"nutrition-item\">\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">膳食纤维</text>\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.fiber }}</text>\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">g</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"nutrition-item\">\n\t\t\t\t\t\t\t<text class=\"nutrition-label\">糖分</text>\n\t\t\t\t\t\t\t<text class=\"nutrition-value\">{{ analysisResult.nutrition.sugar }}</text>\n\t\t\t\t\t\t\t<text class=\"nutrition-unit\">g</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- GI值提示 -->\n\t\t\t\t<view class=\"gi-section\">\n\t\t\t\t\t<view class=\"gi-header\">\n\t\t\t\t\t\t<text class=\"gi-title\">GI值分析</text>\n\t\t\t\t\t\t<view class=\"gi-badge\" :class=\"getGIClass(analysisResult.giValue)\">\n\t\t\t\t\t\t\t<text class=\"gi-value\">GI: {{ analysisResult.giValue }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"gi-description\">\n\t\t\t\t\t\t<text class=\"gi-text\">{{ getGIDescription(analysisResult.giValue) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 详细分析 -->\n\t\t\t\t<view class=\"detailed-analysis\" v-if=\"analysisResult.detailedAnalysis\">\n\n\t\t\t\t\t<!-- 健康建议 -->\n\t\t\t\t\t<view class=\"advice-section\">\n\t\t\t\t\t\t<view class=\"advice-header\">\n\t\t\t\t\t\t\t<image src=\"../../static/resource/images/tips.png\" class=\"advice-icon\"></image>\n\t\t\t\t\t\t\t<text class=\"advice-title\">健康建议</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"advice-content-unified\">\n\t\t\t\t\t\t\t<text class=\"advice-text-unified\">\n\t\t\t\t\t\t\t\t{{ analysisResult.healthAdvice.join('；') }}\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- 推荐食用 -->\n\t\t\t\t\t<view class=\"analysis-section\">\n\t\t\t\t\t\t<view class=\"section-header can-eat\">\n\t\t\t\t\t\t\t<text class=\"section-title\">✅ 推荐食用</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"unified-content\">\n\t\t\t\t\t\t\t<text class=\"unified-text\">{{ analysisResult.detailedAnalysis.canEat.join('；') }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 需要控制 -->\n\t\t\t\t\t<view class=\"analysis-section\">\n\t\t\t\t\t\t<view class=\"section-header limit-eat\">\n\t\t\t\t\t\t\t<text class=\"section-title\">⚠️ 需要控制</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"unified-content\">\n\t\t\t\t\t\t\t<text class=\"unified-text\">{{ analysisResult.detailedAnalysis.limitEat.join('；') }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 改进建议 -->\n\t\t\t\t\t<view class=\"analysis-section\">\n\t\t\t\t\t\t<view class=\"section-header suggestions\">\n\t\t\t\t\t\t\t<text class=\"section-title\">💡 改进建议</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"unified-content\">\n\t\t\t\t\t\t\t<text\n\t\t\t\t\t\t\t\tclass=\"unified-text\">{{ analysisResult.detailedAnalysis.suggestions.join('；') }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\n\t\t\t\t<!-- 保存记录 -->\n\t\t\t\t<view class=\"save-section\">\n\t\t\t\t\t<button class=\"save-btn\" @tap=\"saveAnalysisRecord\">保存到饮食记录</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 历史记录 -->\n\t\t\t<view class=\"history-section\" v-if=\"historyRecords.length > 0\">\n\t\t\t\t<view class=\"history-header\">\n\t\t\t\t\t<text class=\"history-title\">最近分析</text>\n\t\t\t\t\t<text class=\"view-all\" @tap=\"viewAllHistory\">查看全部</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"history-list\">\n\t\t\t\t\t<view class=\"history-item\" v-for=\"record in historyRecords.slice(0, 3)\" :key=\"record.id\"\n\t\t\t\t\t\t@tap=\"viewHistoryDetail(record)\">\n\t\t\t\t\t\t<image :src=\"record.image\" class=\"history-image\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t<view class=\"history-info\">\n\t\t\t\t\t\t\t<text class=\"history-food-name\">{{ record.foodName }}</text>\n\t\t\t\t\t\t\t<text class=\"history-date\">{{ formatDate(record.timestamp) }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"history-gi\" :class=\"getGIClass(record.giValue)\">\n\t\t\t\t\t\t\t<text class=\"history-gi-text\">GI: {{ record.giValue }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup>\n\timport {\n\t\tref,\n\t\tcomputed,\n\t\tonMounted\n\t} from 'vue'\n\timport navbar from '../../components/navbar/navbar.vue'\n\timport {\n\t\tsendMessageToAI\n\t} from '../../utils/ai/service'\n\n\t// 百度智能云菜品识别API配置\n\tconst baiduConfig = {\n\t\tapiKey: '956Ds55wM8lZyOqHc9Xl6Sdj',\n\t\tsecretKey: '6w7lYp7c9pQuDgOqGu0x1Dx8dDAo1HqH',\n\t\ttokenUrl: 'https://aip.baidubce.com/oauth/2.0/token',\n\t\tdishDetectUrl: 'https://aip.baidubce.com/rest/2.0/image-classify/v2/dish'\n\t}\n\n\t// Access Token缓存\n\tlet cachedAccessToken = null\n\tlet tokenExpireTime = 0\n\n\t/**\n\t * 将图片转为 base64\n\t */\n\tfunction getBase64(filePath) {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tuni.getFileSystemManager().readFile({\n\t\t\t\tfilePath,\n\t\t\t\tencoding: 'base64',\n\t\t\t\tsuccess: res => resolve(res.data),\n\t\t\t\tfail: reject\n\t\t\t})\n\t\t})\n\t}\n\n\t/**\n\t * 获取百度API Access Token\n\t */\n\tasync function getBaiduAccessToken() {\n\t\t// 检查缓存的token是否有效\n\t\tconst currentTime = Date.now()\n\t\tif (cachedAccessToken && currentTime < tokenExpireTime) {\n\t\t\treturn cachedAccessToken\n\t\t}\n\n\t\ttry {\n\t\t\tconst response = await uni.request({\n\t\t\t\turl: baiduConfig.tokenUrl,\n\t\t\t\tmethod: 'POST',\n\t\t\t\tdata: {\n\t\t\t\t\tgrant_type: 'client_credentials',\n\t\t\t\t\tclient_id: baiduConfig.apiKey,\n\t\t\t\t\tclient_secret: baiduConfig.secretKey\n\t\t\t\t},\n\t\t\t\theader: {\n\t\t\t\t\t'Content-Type': 'application/x-www-form-urlencoded'\n\t\t\t\t}\n\t\t\t})\n\n\t\t\tif (response.statusCode === 200 && response.data.access_token) {\n\t\t\t\tcachedAccessToken = response.data.access_token\n\t\t\t\t// 设置过期时间（提前5分钟过期以确保安全）\n\t\t\t\ttokenExpireTime = currentTime + (response.data.expires_in - 300) * 1000\n\t\t\t\tconsole.log('百度Access Token获取成功')\n\t\t\t\treturn cachedAccessToken\n\t\t\t} else {\n\t\t\t\tthrow new Error('获取Access Token失败: ' + JSON.stringify(response.data))\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.error('获取百度Access Token失败:', error)\n\t\t\tthrow new Error('百度API认证失败')\n\t\t}\n\t}\n\n\t/**\n\t * 调用百度智能云菜品识别API\n\t */\n\tasync function detectImage(filePath) {\n\t\ttry {\n\t\t\t// 1. 获取Access Token\n\t\t\tconst accessToken = await getBaiduAccessToken()\n\n\t\t\t// 2. 获取图片的base64编码\n\t\t\tconst base64 = await getBase64(filePath)\n\n\t\t\t// 3. 调用百度菜品识别API\n\t\t\tconst response = await uni.request({\n\t\t\t\turl: `${baiduConfig.dishDetectUrl}?access_token=${accessToken}`,\n\t\t\t\tmethod: 'POST',\n\t\t\t\tdata: {\n\t\t\t\t\timage: base64,\n\t\t\t\t\ttop_num: 5,  // 返回前5个识别结果\n\t\t\t\t\tfilter_threshold: 0.7,  // 置信度阈值\n\t\t\t\t\tbaike_num: 2  // 返回百科信息数量\n\t\t\t\t},\n\t\t\t\theader: {\n\t\t\t\t\t'Content-Type': 'application/x-www-form-urlencoded'\n\t\t\t\t}\n\t\t\t})\n\n\t\t\tif (response.statusCode === 200 && response.data) {\n\t\t\t\tconst result = response.data\n\n\t\t\t\t// 检查API调用是否成功\n\t\t\t\tif (result.error_code) {\n\t\t\t\t\tthrow new Error(`百度API错误: ${result.error_msg} (错误码: ${result.error_code})`)\n\t\t\t\t}\n\n\t\t\t\t// 处理识别结果\n\t\t\t\tif (result.result && result.result.length > 0) {\n\t\t\t\t\tconsole.log('百度菜品识别成功:', result)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tsuccess: true,\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tresult: result.result.map(item => ({\n\t\t\t\t\t\t\t\tname: item.name,\n\t\t\t\t\t\t\t\tconfidence: item.probability,\n\t\t\t\t\t\t\t\tcalorie: item.calorie || null,\n\t\t\t\t\t\t\t\tbaike_info: item.baike_info || null\n\t\t\t\t\t\t\t})),\n\t\t\t\t\t\t\tresult_num: result.result_num,\n\t\t\t\t\t\t\tlog_id: result.log_id\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tconsole.warn('百度API未识别到菜品')\n\t\t\t\t\treturn {\n\t\t\t\t\t\tsuccess: false,\n\t\t\t\t\t\tmessage: '未识别到菜品，请确保图片中包含清晰的食物'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tthrow new Error(`API调用失败: HTTP ${response.statusCode}`)\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.error('百度菜品识别失败:', error)\n\t\t\tthrow new Error(`菜品识别失败: ${error.message}`)\n\t\t}\n\t}\n\n\n\n\n\n\t// 响应式数据\n\tconst selectedImage = ref('')\n\tconst analyzing = ref(false)\n\tconst analysisResult = ref(null)\n\tconst historyRecords = ref([])\n\tconst currentStep = ref('') // 当前处理步骤\n\tconst errorMessage = ref('') // 错误信息\n\n\t// 计算属性\n\tconst contentStyle = computed(() => {\n\t\t// 获取菜单按钮位置信息\n\t\tconst menuButtonInfo = uni.getMenuButtonBoundingClientRect()\n\t\tconst navHeight = menuButtonInfo.top + menuButtonInfo.height + 8\n\t\treturn {\n\t\t\tpaddingTop: navHeight + 'px'\n\t\t}\n\t})\n\n\t// 方法\n\tconst chooseImage = () => {\n\t\tuni.chooseImage({\n\t\t\tcount: 1,\n\t\t\tsizeType: ['compressed'],\n\t\t\tsourceType: ['camera', 'album'],\n\t\t\tsuccess: (res) => {\n\t\t\t\tselectedImage.value = res.tempFilePaths[0]\n\t\t\t\tanalysisResult.value = null\n\t\t\t\tconsole.log('图片选择成功:', res.tempFilePaths[0])\n\t\t\t},\n\t\t\tfail: (err) => {\n\t\t\t\tconsole.error('选择图片失败:', err)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '选择图片失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t})\n\t}\n\n\tconst reSelectImage = () => {\n\t\tselectedImage.value = ''\n\t\tanalysisResult.value = null\n\t}\n\n\t/**\n\t * AI分析食物营养成分\n\t */\n\tconst analyzeFood = async () => {\n\t\tif (!selectedImage.value) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '请先选择图片',\n\t\t\t\ticon: 'none'\n\t\t\t})\n\t\t\treturn\n\t\t}\n\n\t\tanalyzing.value = true\n\n\t\ttry {\n\t\t\terrorMessage.value = ''\n\n\t\t\t// 第一步：使用讯飞API识别图片中的食物\n\t\t\tcurrentStep.value = '正在识别食物...'\n\t\t\tconsole.log('开始识别图片中的食物...')\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '正在识别食物...',\n\t\t\t\tmask: true\n\t\t\t})\n\n\t\t\tconst xunfeiResult = await detectImage(selectedImage.value)\n\t\t\tconsole.log('讯飞识别结果:', xunfeiResult)\n\n\t\t\t// 提取识别到的食物信息\n\t\t\tlet foodDescription = ''\n\t\t\tif (xunfeiResult && xunfeiResult.data && xunfeiResult.data.result) {\n\t\t\t\tconst objects = xunfeiResult.data.result\n\t\t\t\tif (objects && objects.length > 0) {\n\t\t\t\t\t// 提取所有识别到的物体名称和置信度\n\t\t\t\t\tconst foodItems = objects\n\t\t\t\t\t\t.filter(item => item.confidence > 0.3) // 过滤低置信度结果\n\t\t\t\t\t\t.map(item => {\n\t\t\t\t\t\t\tconst name = item.name || item.class_name || '未知食物'\n\t\t\t\t\t\t\tconst confidence = Math.round((item.confidence || 0) * 100)\n\t\t\t\t\t\t\treturn `${name}(${confidence}%)`\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.join('、')\n\n\t\t\t\t\tif (foodItems) {\n\t\t\t\t\t\tfoodDescription = `图片中识别到的食物：${foodItems}`\n\t\t\t\t\t} else {\n\t\t\t\t\t\tfoodDescription = '图片中包含食物，但识别置信度较低，正在进行进一步分析'\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tfoodDescription = '图片中包含食物，但具体类型需要进一步分析'\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tfoodDescription = '图片中包含食物，正在进行营养分析'\n\t\t\t}\n\n\t\t\tconsole.log('食物描述:', foodDescription)\n\n\t\t\t// 第二步：将识别结果发送给DeepSeek进行营养分析\n\t\t\tcurrentStep.value = '正在分析营养成分...'\n\t\t\tconsole.log('开始AI营养分析...')\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '正在分析营养成分...',\n\t\t\t\tmask: true\n\t\t\t})\n\n\t\t\tconst aiPrompt = `作为专业的营养师，请分析以下食物的营养成分和健康建议：\n\n${foodDescription}\n\n请严格按照以下JSON格式返回分析结果，不要包含任何其他文字：\n\n{\n  \"foodName\": \"食物名称\",\n  \"confidence\": 85,\n  \"giValue\": 65,\n  \"nutrition\": {\n    \"calories\": 180,\n    \"carbs\": 35,\n    \"protein\": 8,\n    \"fat\": 6,\n    \"fiber\": 4,\n    \"sugar\": 2\n  },\n  \"healthAdvice\": [\n    \"健康建议1\",\n    \"健康建议2\",\n    \"健康建议3\"\n  ],\n  \"detailedAnalysis\": {\n    \"canEat\": [\n      \"推荐食用的食物及原因\"\n    ],\n    \"limitEat\": [\n      \"需要限制的食物及原因\"\n    ],\n    \"suggestions\": [\n      \"具体的饮食建议\"\n    ]\n  }\n}\n\n要求：\n1. 所有数值必须是合理的营养数据\n2. GI值范围在0-100之间\n3. 健康建议要针对糖尿病患者\n4. 分析要专业且实用\n5. 返回纯JSON格式，不要markdown代码块`\n\n\t\t\tconst aiResponse = await sendMessageToAI(aiPrompt)\n\t\t\tconsole.log('AI分析结果:', aiResponse)\n\n\t\t\t// 第三步：解析AI返回的JSON结果\n\t\t\tlet parsedResult\n\t\t\ttry {\n\t\t\t\t// 清理可能的markdown格式\n\t\t\t\tconst cleanedResponse = aiResponse.replace(/```json\\s*|\\s*```/g, '').trim()\n\t\t\t\tparsedResult = JSON.parse(cleanedResponse)\n\t\t\t} catch (parseError) {\n\t\t\t\tconsole.error('JSON解析失败:', parseError)\n\t\t\t\t// 使用备用数据结构\n\t\t\t\tparsedResult = {\n\t\t\t\t\tfoodName: foodDescription.replace('图片中识别到的食物：', '') || \"识别的食物\",\n\t\t\t\t\tconfidence: 80,\n\t\t\t\t\tgiValue: 60,\n\t\t\t\t\tnutrition: {\n\t\t\t\t\t\tcalories: 150,\n\t\t\t\t\t\tcarbs: 30,\n\t\t\t\t\t\tprotein: 6,\n\t\t\t\t\t\tfat: 5,\n\t\t\t\t\t\tfiber: 3,\n\t\t\t\t\t\tsugar: 8\n\t\t\t\t\t},\n\t\t\t\t\thealthAdvice: [\n\t\t\t\t\t\t\"请注意控制食用份量\",\n\t\t\t\t\t\t\"建议搭配低GI食物一起食用\",\n\t\t\t\t\t\t\"餐后适当运动有助于血糖控制\"\n\t\t\t\t\t],\n\t\t\t\t\tdetailedAnalysis: {\n\t\t\t\t\t\tcanEat: [\"根据识别结果，适量食用有益健康\"],\n\t\t\t\t\t\tlimitEat: [\"注意控制高糖高脂食物的摄入\"],\n\t\t\t\t\t\tsuggestions: [\"建议咨询专业营养师制定个性化饮食方案\"]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 设置分析结果\n\t\t\tanalysisResult.value = parsedResult\n\n\t\t\t// 保存到历史记录\n\t\t\tconst newRecord = {\n\t\t\t\tid: Date.now(),\n\t\t\t\timage: selectedImage.value,\n\t\t\t\tfoodName: analysisResult.value.foodName,\n\t\t\t\tgiValue: analysisResult.value.giValue,\n\t\t\t\ttimestamp: Date.now(),\n\t\t\t\tconfidence: analysisResult.value.confidence,\n\t\t\t\tnutrition: analysisResult.value.nutrition,\n\t\t\t\thealthAdvice: analysisResult.value.healthAdvice,\n\t\t\t\tdetailedAnalysis: analysisResult.value.detailedAnalysis,\n\t\t\t\tdate: new Date().toLocaleDateString('zh-CN', {\n\t\t\t\t\tmonth: 'numeric',\n\t\t\t\t\tday: 'numeric',\n\t\t\t\t\thour: '2-digit',\n\t\t\t\t\tminute: '2-digit'\n\t\t\t\t})\n\t\t\t}\n\t\t\tsaveHistoryRecord(newRecord)\n\n\t\t\tuni.hideLoading()\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '分析完成',\n\t\t\t\ticon: 'success'\n\t\t\t})\n\n\t\t} catch (error) {\n\t\t\tconsole.error('分析失败:', error)\n\t\t\tuni.hideLoading()\n\n\t\t\t// 根据错误类型提供不同的提示\n\t\t\tlet errorTitle = '分析失败，请重试'\n\t\t\tif (error.message && error.message.includes('网络')) {\n\t\t\t\terrorTitle = '网络连接失败，请检查网络'\n\t\t\t\terrorMessage.value = '请检查网络连接后重试'\n\t\t\t} else if (error.message && error.message.includes('识别')) {\n\t\t\t\terrorTitle = '图片识别失败'\n\t\t\t\terrorMessage.value = '请选择清晰的食物图片重试'\n\t\t\t} else if (error.message && error.message.includes('AI')) {\n\t\t\t\terrorTitle = 'AI分析服务暂时不可用'\n\t\t\t\terrorMessage.value = '服务暂时繁忙，请稍后重试'\n\t\t\t} else {\n\t\t\t\terrorMessage.value = '分析过程中出现错误，请重新尝试'\n\t\t\t}\n\n\t\t\tuni.showToast({\n\t\t\t\ttitle: errorTitle,\n\t\t\t\ticon: 'none',\n\t\t\t\tduration: 3000\n\t\t\t})\n\t\t} finally {\n\t\t\tanalyzing.value = false\n\t\t\tcurrentStep.value = ''\n\t\t}\n\t}\n\n\t// 获取GI值等级样式类\n\tconst getGIClass = (giValue) => {\n\t\tif (giValue <= 55) return 'low'\n\t\tif (giValue <= 70) return 'medium'\n\t\treturn 'high'\n\t}\n\n\t// 获取GI值描述\n\tconst getGIDescription = (giValue) => {\n\t\tif (giValue <= 55) return '低GI食物，对血糖影响较小，适合糖尿病患者食用'\n\t\tif (giValue <= 70) return '中等GI食物，适量食用，建议搭配低GI食物'\n\t\treturn '高GI食物，会快速升高血糖，糖尿病患者需严格控制'\n\t}\n\n\t// 备用分析结果生成\n\tconst generateBackupAnalysis = (foodName) => {\n\t\tconst foodDatabase = {\n\t\t\t'白米饭': {\n\t\t\t\tfoodName: '白米饭',\n\t\t\t\tconfidence: 95,\n\t\t\t\tgiValue: 83,\n\t\t\t\tnutrition: {\n\t\t\t\t\tcalories: 130,\n\t\t\t\t\tcarbs: 28,\n\t\t\t\t\tprotein: 2.7,\n\t\t\t\t\tfat: 0.3,\n\t\t\t\t\tfiber: 0.4,\n\t\t\t\t\tsugar: 0.1\n\t\t\t\t},\n\t\t\t\thealthAdvice: ['属于高GI食物，会快速升高血糖', '建议搭配蔬菜和蛋白质一起食用', '可以选择糙米或杂粮饭作为替代']\n\t\t\t},\n\t\t\t'苹果': {\n\t\t\t\tfoodName: '苹果',\n\t\t\t\tconfidence: 92,\n\t\t\t\tgiValue: 36,\n\t\t\t\tnutrition: {\n\t\t\t\t\tcalories: 52,\n\t\t\t\t\tcarbs: 14,\n\t\t\t\t\tprotein: 0.3,\n\t\t\t\t\tfat: 0.2,\n\t\t\t\t\tfiber: 2.4,\n\t\t\t\t\tsugar: 10\n\t\t\t\t},\n\t\t\t\thealthAdvice: ['属于低GI食物，对血糖影响较小', '富含膳食纤维，有助于血糖稳定', '建议在两餐之间食用，避免空腹']\n\t\t\t},\n\t\t\t'糙米饭': {\n\t\t\t\tfoodName: '糙米饭',\n\t\t\t\tconfidence: 90,\n\t\t\t\tgiValue: 50,\n\t\t\t\tnutrition: {\n\t\t\t\t\tcalories: 112,\n\t\t\t\t\tcarbs: 23,\n\t\t\t\t\tprotein: 2.6,\n\t\t\t\t\tfat: 0.9,\n\t\t\t\t\tfiber: 1.8,\n\t\t\t\t\tsugar: 0.4\n\t\t\t\t},\n\t\t\t\thealthAdvice: ['中等GI食物，比白米饭更适合糖尿病患者', '富含膳食纤维，有助于血糖稳定', '建议控制食用量，搭配蔬菜食用']\n\t\t\t}\n\t\t}\n\n\t\treturn foodDatabase[foodName] || {\n\t\t\tfoodName: foodName,\n\t\t\tconfidence: 85,\n\t\t\tgiValue: 55,\n\t\t\tnutrition: {\n\t\t\t\tcalories: 100,\n\t\t\t\tcarbs: 20,\n\t\t\t\tprotein: 3,\n\t\t\t\tfat: 1,\n\t\t\t\tfiber: 2,\n\t\t\t\tsugar: 5\n\t\t\t},\n\t\t\thealthAdvice: ['请咨询营养师获取准确信息', '建议适量食用', '注意监测血糖变化']\n\t\t}\n\t}\n\n\n\n\tconst saveAnalysisRecord = () => {\n\t\tif (!analysisResult.value) return\n\n\t\tconst record = {\n\t\t\tid: Date.now(),\n\t\t\timage: selectedImage.value,\n\t\t\tfoodName: analysisResult.value.foodName,\n\t\t\tconfidence: analysisResult.value.confidence,\n\t\t\tgiValue: analysisResult.value.giValue,\n\t\t\tnutrition: analysisResult.value.nutrition,\n\t\t\thealthAdvice: analysisResult.value.healthAdvice,\n\t\t\ttimestamp: new Date().toISOString()\n\t\t}\n\n\t\tconst existingRecords = uni.getStorageSync('foodAnalysisRecords') || []\n\t\texistingRecords.unshift(record)\n\t\tuni.setStorageSync('foodAnalysisRecords', existingRecords)\n\n\t\thistoryRecords.value = existingRecords\n\n\t\tuni.showToast({\n\t\t\ttitle: '保存成功',\n\t\t\ticon: 'success'\n\t\t})\n\t}\n\n\tconst loadHistoryRecords = () => {\n\t\tconst records = uni.getStorageSync('foodAnalysisRecords') || []\n\t\thistoryRecords.value = records\n\t}\n\n\tconst saveHistoryRecord = (record) => {\n\t\ttry {\n\t\t\tconst records = uni.getStorageSync('foodAnalysisRecords') || []\n\t\t\trecords.unshift(record)\n\t\t\t// 只保留最近50条记录\n\t\t\tif (records.length > 50) {\n\t\t\t\trecords.splice(50)\n\t\t\t}\n\t\t\tuni.setStorageSync('foodAnalysisRecords', records)\n\t\t\thistoryRecords.value = records\n\t\t} catch (error) {\n\t\t\tconsole.error('保存历史记录失败:', error)\n\t\t}\n\t}\n\n\tconst formatDate = (timestamp) => {\n\t\tconst date = new Date(timestamp)\n\t\tconst month = date.getMonth() + 1\n\t\tconst day = date.getDate()\n\t\tconst hours = date.getHours().toString().padStart(2, '0')\n\t\tconst minutes = date.getMinutes().toString().padStart(2, '0')\n\t\treturn `${month}月${day}日 ${hours}:${minutes}`\n\t}\n\n\tconst viewHistoryDetail = (record) => {\n\t\t// 显示历史记录详情\n\t\tanalysisResult.value = record\n\t\tselectedImage.value = record.image\n\t}\n\n\tconst viewAllHistory = () => {\n\t\tuni.navigateTo({\n\t\t\turl: '/pages/blood-glucose/food-history'\n\t\t})\n\t}\n\n\tonMounted(() => {\n\t\tloadHistoryRecords()\n\t})\n</script>\n\n<style scoped>\n\t.page-container {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f5f7fa;\n\t}\n\n\t.content-container {\n\t\tpadding: 20rpx;\n\t}\n\n\t/* 上传区域 */\n\t.upload-section {\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.upload-header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.upload-title {\n\t\tdisplay: block;\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 12rpx;\n\t}\n\n\t.upload-subtitle {\n\t\tdisplay: block;\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n\n\t.upload-area {\n\t\tborder: 3rpx dashed #e0e0e0;\n\t\tborder-radius: 16rpx;\n\t\tmin-height: 400rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-bottom: 30rpx;\n\t\ttransition: all 0.3s;\n\t}\n\n\t.upload-area:active {\n\t\tborder-color: #00b38a;\n\t\tbackground: rgba(0, 179, 138, 0.05);\n\t}\n\n\t.preview-image {\n\t\twidth: 100%;\n\t\theight: 400rpx;\n\t\tborder-radius: 12rpx;\n\t}\n\n\t.upload-placeholder {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tgap: 16rpx;\n\t}\n\n\t.upload-icon {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\topacity: 0.6;\n\t}\n\n\t.upload-text {\n\t\tfont-size: 32rpx;\n\t\tcolor: #666;\n\t}\n\n\t.upload-hint {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t}\n\n\t.upload-actions {\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t}\n\n\t.action-btn {\n\t\tflex: 1;\n\t\tpadding: 24rpx;\n\t\tborder-radius: 12rpx;\n\t\tfont-size: 28rpx;\n\t\tborder: none;\n\t}\n\n\t.action-btn.secondary {\n\t\tbackground: #f5f7fa;\n\t\tcolor: #666;\n\t}\n\n\t.action-btn.primary {\n\t\tbackground: #00b38a;\n\t\tcolor: white;\n\t}\n\n\t.action-btn:disabled {\n\t\topacity: 0.6;\n\t}\n\n\t/* 分析结果 */\n\t.analysis-result {\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.result-header {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.result-title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\n\t/* 食物信息 */\n\t.food-info {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.food-name {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 20rpx;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 12rpx;\n\t}\n\n\t.name-text {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\n\t.confidence-badge {\n\t\tbackground: #00b38a;\n\t\tcolor: white;\n\t\tpadding: 8rpx 16rpx;\n\t\tborder-radius: 20rpx;\n\t}\n\n\t.confidence-text {\n\t\tfont-size: 24rpx;\n\t}\n\n\t/* 营养成分 */\n\t.nutrition-section {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.section-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.nutrition-grid {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: 1fr 1fr;\n\t\tgap: 20rpx;\n\t}\n\n\t.nutrition-item {\n\t\ttext-align: center;\n\t\tpadding: 20rpx;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 12rpx;\n\t}\n\n\t.nutrition-label {\n\t\tdisplay: block;\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tmargin-bottom: 8rpx;\n\t}\n\n\t.nutrition-value {\n\t\tdisplay: block;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #00b38a;\n\t\tmargin-bottom: 4rpx;\n\t}\n\n\t.nutrition-unit {\n\t\tdisplay: block;\n\t\tfont-size: 20rpx;\n\t\tcolor: #999;\n\t}\n\n\t/* GI值分析 */\n\t.gi-section {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.gi-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: 16rpx;\n\t}\n\n\t.gi-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\n\t.gi-badge {\n\t\tpadding: 8rpx 16rpx;\n\t\tborder-radius: 20rpx;\n\t\tcolor: white;\n\t}\n\n\t.gi-badge.low {\n\t\tbackground: #4CAF50;\n\t}\n\n\t.gi-badge.medium {\n\t\tbackground: #ff9800;\n\t}\n\n\t.gi-badge.high {\n\t\tbackground: #f44336;\n\t}\n\n\t.gi-value {\n\t\tfont-size: 24rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.gi-description {\n\t\tpadding: 20rpx;\n\t\tbackground: #f0f8ff;\n\t\tborder-radius: 12rpx;\n\t\tborder-left: 6rpx solid #00b38a;\n\t}\n\n\t.gi-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tline-height: 1.6;\n\t}\n\n\t/* 健康建议 */\n\t.advice-section {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.advice-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 16rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.advice-icon {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t}\n\n\t.advice-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\n\t.advice-content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 16rpx;\n\t}\n\n\t.advice-text {\n\t\tpadding: 20rpx;\n\t\tbackground: #fff3cd;\n\t\tborder-radius: 12rpx;\n\t\tborder-left: 6rpx solid #ffc107;\n\t\tfont-size: 28rpx;\n\t\tcolor: #856404;\n\t\tline-height: 1.6;\n\t}\n\n\t.advice-content-unified {\n\t\tpadding: 24rpx;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 12rpx;\n\t\tborder-left: 6rpx solid #00b38a;\n\t}\n\n\t.advice-text-unified {\n\t\tfont-size: 28rpx;\n\t\tcolor: #555;\n\t\tline-height: 1.8;\n\t\ttext-align: justify;\n\t}\n\n\t/* 详细分析 */\n\t.detailed-analysis {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.analysis-section {\n\t\tmargin-bottom: 24rpx;\n\t}\n\n\t.section-header {\n\t\tpadding: 16rpx 20rpx;\n\t\tborder-radius: 12rpx;\n\t\tmargin-bottom: 16rpx;\n\t}\n\n\t.section-header.can-eat {\n\t\tbackground: rgba(76, 175, 80, 0.1);\n\t\tborder-left: 6rpx solid #4CAF50;\n\t}\n\n\t.section-header.limit-eat {\n\t\tbackground: rgba(255, 152, 0, 0.1);\n\t\tborder-left: 6rpx solid #ff9800;\n\t}\n\n\t.section-header.suggestions {\n\t\tbackground: rgba(0, 179, 138, 0.1);\n\t\tborder-left: 6rpx solid #00b38a;\n\t}\n\n\t.section-title {\n\t\tfont-size: 30rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\n\t.food-list {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 12rpx;\n\t}\n\n\t.food-item {\n\t\tpadding: 16rpx 20rpx;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 8rpx;\n\t\tborder-left: 4rpx solid #e0e0e0;\n\t}\n\n\t.food-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #555;\n\t\tline-height: 1.5;\n\t}\n\n\t.unified-content {\n\t\tpadding: 20rpx;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 8rpx;\n\t\tmargin-top: 12rpx;\n\t}\n\n\t.unified-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #555;\n\t\tline-height: 1.6;\n\t\ttext-align: justify;\n\t}\n\n\t/* 保存按钮 */\n\t.save-section {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.save-btn {\n\t\twidth: 100%;\n\t\tpadding: 24rpx;\n\t\tbackground: #00b38a;\n\t\tcolor: white;\n\t\tborder: none;\n\t\tborder-radius: 12rpx;\n\t\tfont-size: 28rpx;\n\t}\n\n\t/* 历史记录 */\n\t.history-section {\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx;\n\t}\n\n\t.history-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.history-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\n\t.view-all {\n\t\tfont-size: 28rpx;\n\t\tcolor: #00b38a;\n\t}\n\n\t.history-list {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 16rpx;\n\t}\n\n\t.history-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 20rpx;\n\t\tpadding: 20rpx;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 12rpx;\n\t}\n\n\t.history-image {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tborder-radius: 8rpx;\n\t}\n\n\t.history-info {\n\t\tflex: 1;\n\t}\n\n\t.history-food-name {\n\t\tdisplay: block;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tmargin-bottom: 8rpx;\n\t}\n\n\t.history-date {\n\t\tdisplay: block;\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t}\n\n\t.history-gi {\n\t\tpadding: 6rpx 12rpx;\n\t\tborder-radius: 16rpx;\n\t\tcolor: white;\n\t}\n\n\t.history-gi.low {\n\t\tbackground: #4CAF50;\n\t}\n\n\t.history-gi.medium {\n\t\tbackground: #ff9800;\n\t}\n\n\t.history-gi.high {\n\t\tbackground: #f44336;\n\t}\n\n\t.history-gi-text {\n\t\tfont-size: 20rpx;\n\t}\n\n\t/* 分析状态样式 */\n\t.analysis-status {\n\t\tmargin-top: 20rpx;\n\t\tpadding: 20rpx;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 12rpx;\n\t}\n\n\t.status-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 12rpx;\n\t}\n\n\t.loading-icon {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t\tborder: 3rpx solid #e0e0e0;\n\t\tborder-top: 3rpx solid #3b82f6;\n\t\tborder-radius: 50%;\n\t\tanimation: spin 1s linear infinite;\n\t}\n\n\t@keyframes spin {\n\t\t0% { transform: rotate(0deg); }\n\t\t100% { transform: rotate(360deg); }\n\t}\n\n\t.status-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #3b82f6;\n\t}\n\n\t.error-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 12rpx;\n\t\tmargin-top: 12rpx;\n\t}\n\n\t.error-icon {\n\t\tfont-size: 32rpx;\n\t}\n\n\t.error-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #f44336;\n\t\tflex: 1;\n\t}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/2025077166-源代码/pz/pages/blood-glucose/food-analysis.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "ref", "computed", "sendMessageToAI", "onMounted"], "mappings": ";;;;;;;AAmMC,MAAM,SAAS,MAAW;;;;AAM1B,UAAM,cAAc;AAAA,MACnB,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,UAAU;AAAA,MACV,eAAe;AAAA,IACf;AAGD,QAAI,oBAAoB;AACxB,QAAI,kBAAkB;AAKtB,aAAS,UAAU,UAAU;AAC5B,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCA,4BAAI,qBAAsB,EAAC,SAAS;AAAA,UACnC;AAAA,UACA,UAAU;AAAA,UACV,SAAS,SAAO,QAAQ,IAAI,IAAI;AAAA,UAChC,MAAM;AAAA,QACV,CAAI;AAAA,MACJ,CAAG;AAAA,IACD;AAKD,mBAAe,sBAAsB;AAEpC,YAAM,cAAc,KAAK,IAAK;AAC9B,UAAI,qBAAqB,cAAc,iBAAiB;AACvD,eAAO;AAAA,MACP;AAED,UAAI;AACH,cAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,UAClC,KAAK,YAAY;AAAA,UACjB,QAAQ;AAAA,UACR,MAAM;AAAA,YACL,YAAY;AAAA,YACZ,WAAW,YAAY;AAAA,YACvB,eAAe,YAAY;AAAA,UAC3B;AAAA,UACD,QAAQ;AAAA,YACP,gBAAgB;AAAA,UAChB;AAAA,QACL,CAAI;AAED,YAAI,SAAS,eAAe,OAAO,SAAS,KAAK,cAAc;AAC9D,8BAAoB,SAAS,KAAK;AAElC,4BAAkB,eAAe,SAAS,KAAK,aAAa,OAAO;AACnEA,wBAAAA,MAAY,MAAA,OAAA,gDAAA,oBAAoB;AAChC,iBAAO;AAAA,QACX,OAAU;AACN,gBAAM,IAAI,MAAM,uBAAuB,KAAK,UAAU,SAAS,IAAI,CAAC;AAAA,QACpE;AAAA,MACD,SAAQ,OAAO;AACfA,sBAAAA,MAAc,MAAA,SAAA,gDAAA,uBAAuB,KAAK;AAC1C,cAAM,IAAI,MAAM,WAAW;AAAA,MAC3B;AAAA,IACD;AAKD,mBAAe,YAAY,UAAU;AACpC,UAAI;AAEH,cAAM,cAAc,MAAM,oBAAqB;AAG/C,cAAM,SAAS,MAAM,UAAU,QAAQ;AAGvC,cAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,UAClC,KAAK,GAAG,YAAY,aAAa,iBAAiB,WAAW;AAAA,UAC7D,QAAQ;AAAA,UACR,MAAM;AAAA,YACL,OAAO;AAAA,YACP,SAAS;AAAA;AAAA,YACT,kBAAkB;AAAA;AAAA,YAClB,WAAW;AAAA;AAAA,UACX;AAAA,UACD,QAAQ;AAAA,YACP,gBAAgB;AAAA,UAChB;AAAA,QACL,CAAI;AAED,YAAI,SAAS,eAAe,OAAO,SAAS,MAAM;AACjD,gBAAM,SAAS,SAAS;AAGxB,cAAI,OAAO,YAAY;AACtB,kBAAM,IAAI,MAAM,YAAY,OAAO,SAAS,UAAU,OAAO,UAAU,GAAG;AAAA,UAC1E;AAGD,cAAI,OAAO,UAAU,OAAO,OAAO,SAAS,GAAG;AAC9CA,0BAAAA,MAAA,MAAA,OAAA,gDAAY,aAAa,MAAM;AAC/B,mBAAO;AAAA,cACN,SAAS;AAAA,cACT,MAAM;AAAA,gBACL,QAAQ,OAAO,OAAO,IAAI,WAAS;AAAA,kBAClC,MAAM,KAAK;AAAA,kBACX,YAAY,KAAK;AAAA,kBACjB,SAAS,KAAK,WAAW;AAAA,kBACzB,YAAY,KAAK,cAAc;AAAA,gBACvC,EAAS;AAAA,gBACF,YAAY,OAAO;AAAA,gBACnB,QAAQ,OAAO;AAAA,cACf;AAAA,YACD;AAAA,UACN,OAAW;AACNA,0BAAAA,MAAa,MAAA,QAAA,gDAAA,aAAa;AAC1B,mBAAO;AAAA,cACN,SAAS;AAAA,cACT,SAAS;AAAA,YACT;AAAA,UACD;AAAA,QACL,OAAU;AACN,gBAAM,IAAI,MAAM,iBAAiB,SAAS,UAAU,EAAE;AAAA,QACtD;AAAA,MACD,SAAQ,OAAO;AACfA,sBAAAA,MAAc,MAAA,SAAA,gDAAA,aAAa,KAAK;AAChC,cAAM,IAAI,MAAM,WAAW,MAAM,OAAO,EAAE;AAAA,MAC1C;AAAA,IACD;AAOD,UAAM,gBAAgBC,cAAG,IAAC,EAAE;AAC5B,UAAM,YAAYA,cAAG,IAAC,KAAK;AAC3B,UAAM,iBAAiBA,cAAG,IAAC,IAAI;AAC/B,UAAM,iBAAiBA,cAAG,IAAC,EAAE;AAC7B,UAAM,cAAcA,cAAG,IAAC,EAAE;AAC1B,UAAM,eAAeA,cAAG,IAAC,EAAE;AAG3B,UAAM,eAAeC,cAAAA,SAAS,MAAM;AAEnC,YAAM,iBAAiBF,cAAG,MAAC,gCAAiC;AAC5D,YAAM,YAAY,eAAe,MAAM,eAAe,SAAS;AAC/D,aAAO;AAAA,QACN,YAAY,YAAY;AAAA,MACxB;AAAA,IACH,CAAE;AAGD,UAAM,cAAc,MAAM;AACzBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,UAAU,OAAO;AAAA,QAC9B,SAAS,CAAC,QAAQ;AACjB,wBAAc,QAAQ,IAAI,cAAc,CAAC;AACzC,yBAAe,QAAQ;AACvBA,8BAAA,MAAA,OAAA,gDAAY,WAAW,IAAI,cAAc,CAAC,CAAC;AAAA,QAC3C;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,wBAAAA,qEAAc,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACX,CAAK;AAAA,QACD;AAAA,MACJ,CAAG;AAAA,IACD;AAED,UAAM,gBAAgB,MAAM;AAC3B,oBAAc,QAAQ;AACtB,qBAAe,QAAQ;AAAA,IACvB;AAKD,UAAM,cAAc,YAAY;AAC/B,UAAI,CAAC,cAAc,OAAO;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAI;AACD;AAAA,MACA;AAED,gBAAU,QAAQ;AAElB,UAAI;AACH,qBAAa,QAAQ;AAGrB,oBAAY,QAAQ;AACpBA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,eAAe;AAC3BA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAI;AAED,cAAM,eAAe,MAAM,YAAY,cAAc,KAAK;AAC1DA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,WAAW,YAAY;AAGnC,YAAI,kBAAkB;AACtB,YAAI,gBAAgB,aAAa,QAAQ,aAAa,KAAK,QAAQ;AAClE,gBAAM,UAAU,aAAa,KAAK;AAClC,cAAI,WAAW,QAAQ,SAAS,GAAG;AAElC,kBAAM,YAAY,QAChB,OAAO,UAAQ,KAAK,aAAa,GAAG,EACpC,IAAI,UAAQ;AACZ,oBAAM,OAAO,KAAK,QAAQ,KAAK,cAAc;AAC7C,oBAAM,aAAa,KAAK,OAAO,KAAK,cAAc,KAAK,GAAG;AAC1D,qBAAO,GAAG,IAAI,IAAI,UAAU;AAAA,YACnC,CAAO,EACA,KAAK,GAAG;AAEV,gBAAI,WAAW;AACd,gCAAkB,aAAa,SAAS;AAAA,YAC9C,OAAY;AACN,gCAAkB;AAAA,YAClB;AAAA,UACN,OAAW;AACN,8BAAkB;AAAA,UAClB;AAAA,QACL,OAAU;AACN,4BAAkB;AAAA,QAClB;AAEDA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,SAAS,eAAe;AAGpC,oBAAY,QAAQ;AACpBA,sBAAAA,MAAA,MAAA,OAAA,gDAAY,aAAa;AACzBA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAI;AAED,cAAM,WAAW;AAAA;AAAA,EAElB,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyCd,cAAM,aAAa,MAAMG,iBAAe,gBAAC,QAAQ;AACjDH,sBAAAA,MAAA,MAAA,OAAA,gDAAY,WAAW,UAAU;AAGjC,YAAI;AACJ,YAAI;AAEH,gBAAM,kBAAkB,WAAW,QAAQ,sBAAsB,EAAE,EAAE,KAAM;AAC3E,yBAAe,KAAK,MAAM,eAAe;AAAA,QACzC,SAAQ,YAAY;AACpBA,wBAAAA,MAAc,MAAA,SAAA,gDAAA,aAAa,UAAU;AAErC,yBAAe;AAAA,YACd,UAAU,gBAAgB,QAAQ,cAAc,EAAE,KAAK;AAAA,YACvD,YAAY;AAAA,YACZ,SAAS;AAAA,YACT,WAAW;AAAA,cACV,UAAU;AAAA,cACV,OAAO;AAAA,cACP,SAAS;AAAA,cACT,KAAK;AAAA,cACL,OAAO;AAAA,cACP,OAAO;AAAA,YACP;AAAA,YACD,cAAc;AAAA,cACb;AAAA,cACA;AAAA,cACA;AAAA,YACA;AAAA,YACD,kBAAkB;AAAA,cACjB,QAAQ,CAAC,iBAAiB;AAAA,cAC1B,UAAU,CAAC,eAAe;AAAA,cAC1B,aAAa,CAAC,oBAAoB;AAAA,YAClC;AAAA,UACD;AAAA,QACD;AAGD,uBAAe,QAAQ;AAGvB,cAAM,YAAY;AAAA,UACjB,IAAI,KAAK,IAAK;AAAA,UACd,OAAO,cAAc;AAAA,UACrB,UAAU,eAAe,MAAM;AAAA,UAC/B,SAAS,eAAe,MAAM;AAAA,UAC9B,WAAW,KAAK,IAAK;AAAA,UACrB,YAAY,eAAe,MAAM;AAAA,UACjC,WAAW,eAAe,MAAM;AAAA,UAChC,cAAc,eAAe,MAAM;AAAA,UACnC,kBAAkB,eAAe,MAAM;AAAA,UACvC,OAAM,oBAAI,QAAO,mBAAmB,SAAS;AAAA,YAC5C,OAAO;AAAA,YACP,KAAK;AAAA,YACL,MAAM;AAAA,YACN,QAAQ;AAAA,UACb,CAAK;AAAA,QACD;AACD,0BAAkB,SAAS;AAE3BA,sBAAAA,MAAI,YAAa;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAI;AAAA,MAED,SAAQ,OAAO;AACfA,sBAAAA,MAAA,MAAA,SAAA,gDAAc,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,YAAa;AAGjB,YAAI,aAAa;AACjB,YAAI,MAAM,WAAW,MAAM,QAAQ,SAAS,IAAI,GAAG;AAClD,uBAAa;AACb,uBAAa,QAAQ;AAAA,QACzB,WAAc,MAAM,WAAW,MAAM,QAAQ,SAAS,IAAI,GAAG;AACzD,uBAAa;AACb,uBAAa,QAAQ;AAAA,QACzB,WAAc,MAAM,WAAW,MAAM,QAAQ,SAAS,IAAI,GAAG;AACzD,uBAAa;AACb,uBAAa,QAAQ;AAAA,QACzB,OAAU;AACN,uBAAa,QAAQ;AAAA,QACrB;AAEDA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACd,CAAI;AAAA,MACJ,UAAY;AACT,kBAAU,QAAQ;AAClB,oBAAY,QAAQ;AAAA,MACpB;AAAA,IACD;AAGD,UAAM,aAAa,CAAC,YAAY;AAC/B,UAAI,WAAW;AAAI,eAAO;AAC1B,UAAI,WAAW;AAAI,eAAO;AAC1B,aAAO;AAAA,IACP;AAGD,UAAM,mBAAmB,CAAC,YAAY;AACrC,UAAI,WAAW;AAAI,eAAO;AAC1B,UAAI,WAAW;AAAI,eAAO;AAC1B,aAAO;AAAA,IACP;AAmED,UAAM,qBAAqB,MAAM;AAChC,UAAI,CAAC,eAAe;AAAO;AAE3B,YAAM,SAAS;AAAA,QACd,IAAI,KAAK,IAAK;AAAA,QACd,OAAO,cAAc;AAAA,QACrB,UAAU,eAAe,MAAM;AAAA,QAC/B,YAAY,eAAe,MAAM;AAAA,QACjC,SAAS,eAAe,MAAM;AAAA,QAC9B,WAAW,eAAe,MAAM;AAAA,QAChC,cAAc,eAAe,MAAM;AAAA,QACnC,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,MACnC;AAED,YAAM,kBAAkBA,cAAG,MAAC,eAAe,qBAAqB,KAAK,CAAE;AACvE,sBAAgB,QAAQ,MAAM;AAC9BA,0BAAI,eAAe,uBAAuB,eAAe;AAEzD,qBAAe,QAAQ;AAEvBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACT,CAAG;AAAA,IACD;AAED,UAAM,qBAAqB,MAAM;AAChC,YAAM,UAAUA,cAAG,MAAC,eAAe,qBAAqB,KAAK,CAAE;AAC/D,qBAAe,QAAQ;AAAA,IACvB;AAED,UAAM,oBAAoB,CAAC,WAAW;AACrC,UAAI;AACH,cAAM,UAAUA,cAAG,MAAC,eAAe,qBAAqB,KAAK,CAAE;AAC/D,gBAAQ,QAAQ,MAAM;AAEtB,YAAI,QAAQ,SAAS,IAAI;AACxB,kBAAQ,OAAO,EAAE;AAAA,QACjB;AACDA,4BAAI,eAAe,uBAAuB,OAAO;AACjD,uBAAe,QAAQ;AAAA,MACvB,SAAQ,OAAO;AACfA,sBAAAA,MAAc,MAAA,SAAA,gDAAA,aAAa,KAAK;AAAA,MAChC;AAAA,IACD;AAED,UAAM,aAAa,CAAC,cAAc;AACjC,YAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,YAAM,QAAQ,KAAK,SAAQ,IAAK;AAChC,YAAM,MAAM,KAAK,QAAS;AAC1B,YAAM,QAAQ,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACxD,YAAM,UAAU,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC5D,aAAO,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,OAAO;AAAA,IAC3C;AAED,UAAM,oBAAoB,CAAC,WAAW;AAErC,qBAAe,QAAQ;AACvB,oBAAc,QAAQ,OAAO;AAAA,IAC7B;AAED,UAAM,iBAAiB,MAAM;AAC5BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACR,CAAG;AAAA,IACD;AAEDI,kBAAAA,UAAU,MAAM;AACf,yBAAoB;AAAA,IACtB,CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1tBF,GAAG,WAAW,eAAe;"}