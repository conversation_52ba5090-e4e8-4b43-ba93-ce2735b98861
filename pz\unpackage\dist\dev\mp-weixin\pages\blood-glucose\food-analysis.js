"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const utils_ai_service = require("../../utils/ai/service.js");
if (!Math) {
  navbar();
}
const navbar = () => "../../components/navbar/navbar.js";
const _sfc_main = {
  __name: "food-analysis",
  setup(__props) {
    const baiduConfig = {
      apiKey: "956Ds55wM8lZyOqHc9Xl6Sdj",
      secretKey: "6w7lYp7c9pQuDgOqGu0x1Dx8dDAo1HqH",
      tokenUrl: "https://aip.baidubce.com/oauth/2.0/token",
      dishDetectUrl: "https://aip.baidubce.com/rest/2.0/image-classify/v2/dish"
    };
    let cachedAccessToken = null;
    let tokenExpireTime = 0;
    function getBase64(filePath) {
      return new Promise((resolve, reject) => {
        common_vendor.index.getFileSystemManager().readFile({
          filePath,
          encoding: "base64",
          success: (res) => resolve(res.data),
          fail: reject
        });
      });
    }
    async function getBaiduAccessToken() {
      const currentTime = Date.now();
      if (cachedAccessToken && currentTime < tokenExpireTime) {
        return cachedAccessToken;
      }
      try {
        const response = await common_vendor.index.request({
          url: baiduConfig.tokenUrl,
          method: "POST",
          data: {
            grant_type: "client_credentials",
            client_id: baiduConfig.apiKey,
            client_secret: baiduConfig.secretKey
          },
          header: {
            "Content-Type": "application/x-www-form-urlencoded"
          }
        });
        if (response.statusCode === 200 && response.data.access_token) {
          cachedAccessToken = response.data.access_token;
          tokenExpireTime = currentTime + (response.data.expires_in - 300) * 1e3;
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:255", "百度Access Token获取成功");
          return cachedAccessToken;
        } else {
          throw new Error("获取Access Token失败: " + JSON.stringify(response.data));
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:261", "获取百度Access Token失败:", error);
        throw new Error("百度API认证失败");
      }
    }
    async function detectImage(filePath) {
      try {
        const accessToken = await getBaiduAccessToken();
        const base64 = await getBase64(filePath);
        const response = await common_vendor.index.request({
          url: `${baiduConfig.dishDetectUrl}?access_token=${accessToken}`,
          method: "POST",
          data: {
            image: base64,
            top_num: 5,
            // 返回前5个识别结果
            filter_threshold: 0.7,
            // 置信度阈值
            baike_num: 2
            // 返回百科信息数量
          },
          header: {
            "Content-Type": "application/x-www-form-urlencoded"
          }
        });
        if (response.statusCode === 200 && response.data) {
          const result = response.data;
          if (result.error_code) {
            throw new Error(`百度API错误: ${result.error_msg} (错误码: ${result.error_code})`);
          }
          if (result.result && result.result.length > 0) {
            common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:302", "百度菜品识别成功:", result);
            return {
              success: true,
              data: {
                result: result.result.map((item) => ({
                  name: item.name,
                  confidence: item.probability,
                  calorie: item.calorie || null,
                  baike_info: item.baike_info || null
                })),
                result_num: result.result_num,
                log_id: result.log_id
              }
            };
          } else {
            common_vendor.index.__f__("warn", "at pages/blood-glucose/food-analysis.vue:317", "百度API未识别到菜品");
            return {
              success: false,
              message: "未识别到菜品，请确保图片中包含清晰的食物"
            };
          }
        } else {
          throw new Error(`API调用失败: HTTP ${response.statusCode}`);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:327", "百度菜品识别失败:", error);
        throw new Error(`菜品识别失败: ${error.message}`);
      }
    }
    const selectedImage = common_vendor.ref("");
    const analyzing = common_vendor.ref(false);
    const analysisResult = common_vendor.ref(null);
    const historyRecords = common_vendor.ref([]);
    const currentStep = common_vendor.ref("");
    const errorMessage = common_vendor.ref("");
    const contentStyle = common_vendor.computed(() => {
      const menuButtonInfo = common_vendor.index.getMenuButtonBoundingClientRect();
      const navHeight = menuButtonInfo.top + menuButtonInfo.height + 8;
      return {
        paddingTop: navHeight + "px"
      };
    });
    const chooseImage = () => {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["camera", "album"],
        success: (res) => {
          selectedImage.value = res.tempFilePaths[0];
          analysisResult.value = null;
          common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:363", "图片选择成功:", res.tempFilePaths[0]);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:366", "选择图片失败:", err);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    };
    const reSelectImage = () => {
      selectedImage.value = "";
      analysisResult.value = null;
    };
    const analyzeFood = async () => {
      if (!selectedImage.value) {
        common_vendor.index.showToast({
          title: "请先选择图片",
          icon: "none"
        });
        return;
      }
      analyzing.value = true;
      try {
        errorMessage.value = "";
        currentStep.value = "正在识别食物...";
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:399", "开始识别图片中的食物...");
        common_vendor.index.showLoading({
          title: "正在识别食物...",
          mask: true
        });
        const xunfeiResult = await detectImage(selectedImage.value);
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:406", "讯飞识别结果:", xunfeiResult);
        let foodDescription = "";
        if (xunfeiResult && xunfeiResult.data && xunfeiResult.data.result) {
          const objects = xunfeiResult.data.result;
          if (objects && objects.length > 0) {
            const foodItems = objects.filter((item) => item.confidence > 0.3).map((item) => {
              const name = item.name || item.class_name || "未知食物";
              const confidence = Math.round((item.confidence || 0) * 100);
              return `${name}(${confidence}%)`;
            }).join("、");
            if (foodItems) {
              foodDescription = `图片中识别到的食物：${foodItems}`;
            } else {
              foodDescription = "图片中包含食物，但识别置信度较低，正在进行进一步分析";
            }
          } else {
            foodDescription = "图片中包含食物，但具体类型需要进一步分析";
          }
        } else {
          foodDescription = "图片中包含食物，正在进行营养分析";
        }
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:435", "食物描述:", foodDescription);
        currentStep.value = "正在分析营养成分...";
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:439", "开始AI营养分析...");
        common_vendor.index.showLoading({
          title: "正在分析营养成分...",
          mask: true
        });
        const aiPrompt = `作为专业的营养师，请分析以下食物的营养成分和健康建议：

${foodDescription}

请严格按照以下JSON格式返回分析结果，不要包含任何其他文字：

{
  "foodName": "食物名称",
  "confidence": 85,
  "giValue": 65,
  "nutrition": {
    "calories": 180,
    "carbs": 35,
    "protein": 8,
    "fat": 6,
    "fiber": 4,
    "sugar": 2
  },
  "healthAdvice": [
    "健康建议1",
    "健康建议2",
    "健康建议3"
  ],
  "detailedAnalysis": {
    "canEat": [
      "推荐食用的食物及原因"
    ],
    "limitEat": [
      "需要限制的食物及原因"
    ],
    "suggestions": [
      "具体的饮食建议"
    ]
  }
}

要求：
1. 所有数值必须是合理的营养数据
2. GI值范围在0-100之间
3. 健康建议要针对糖尿病患者
4. 分析要专业且实用
5. 返回纯JSON格式，不要markdown代码块`;
        const aiResponse = await utils_ai_service.sendMessageToAI(aiPrompt);
        common_vendor.index.__f__("log", "at pages/blood-glucose/food-analysis.vue:489", "AI分析结果:", aiResponse);
        let parsedResult;
        try {
          const cleanedResponse = aiResponse.replace(/```json\s*|\s*```/g, "").trim();
          parsedResult = JSON.parse(cleanedResponse);
        } catch (parseError) {
          common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:498", "JSON解析失败:", parseError);
          parsedResult = {
            foodName: foodDescription.replace("图片中识别到的食物：", "") || "识别的食物",
            confidence: 80,
            giValue: 60,
            nutrition: {
              calories: 150,
              carbs: 30,
              protein: 6,
              fat: 5,
              fiber: 3,
              sugar: 8
            },
            healthAdvice: [
              "请注意控制食用份量",
              "建议搭配低GI食物一起食用",
              "餐后适当运动有助于血糖控制"
            ],
            detailedAnalysis: {
              canEat: ["根据识别结果，适量食用有益健康"],
              limitEat: ["注意控制高糖高脂食物的摄入"],
              suggestions: ["建议咨询专业营养师制定个性化饮食方案"]
            }
          };
        }
        analysisResult.value = parsedResult;
        const newRecord = {
          id: Date.now(),
          image: selectedImage.value,
          foodName: analysisResult.value.foodName,
          giValue: analysisResult.value.giValue,
          timestamp: Date.now(),
          confidence: analysisResult.value.confidence,
          nutrition: analysisResult.value.nutrition,
          healthAdvice: analysisResult.value.healthAdvice,
          detailedAnalysis: analysisResult.value.detailedAnalysis,
          date: (/* @__PURE__ */ new Date()).toLocaleDateString("zh-CN", {
            month: "numeric",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit"
          })
        };
        saveHistoryRecord(newRecord);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "分析完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:555", "分析失败:", error);
        common_vendor.index.hideLoading();
        let errorTitle = "分析失败，请重试";
        if (error.message && error.message.includes("网络")) {
          errorTitle = "网络连接失败，请检查网络";
          errorMessage.value = "请检查网络连接后重试";
        } else if (error.message && error.message.includes("识别")) {
          errorTitle = "图片识别失败";
          errorMessage.value = "请选择清晰的食物图片重试";
        } else if (error.message && error.message.includes("AI")) {
          errorTitle = "AI分析服务暂时不可用";
          errorMessage.value = "服务暂时繁忙，请稍后重试";
        } else {
          errorMessage.value = "分析过程中出现错误，请重新尝试";
        }
        common_vendor.index.showToast({
          title: errorTitle,
          icon: "none",
          duration: 3e3
        });
      } finally {
        analyzing.value = false;
        currentStep.value = "";
      }
    };
    const getGIClass = (giValue) => {
      if (giValue <= 55)
        return "low";
      if (giValue <= 70)
        return "medium";
      return "high";
    };
    const getGIDescription = (giValue) => {
      if (giValue <= 55)
        return "低GI食物，对血糖影响较小，适合糖尿病患者食用";
      if (giValue <= 70)
        return "中等GI食物，适量食用，建议搭配低GI食物";
      return "高GI食物，会快速升高血糖，糖尿病患者需严格控制";
    };
    const saveAnalysisRecord = () => {
      if (!analysisResult.value)
        return;
      const record = {
        id: Date.now(),
        image: selectedImage.value,
        foodName: analysisResult.value.foodName,
        confidence: analysisResult.value.confidence,
        giValue: analysisResult.value.giValue,
        nutrition: analysisResult.value.nutrition,
        healthAdvice: analysisResult.value.healthAdvice,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
      const existingRecords = common_vendor.index.getStorageSync("foodAnalysisRecords") || [];
      existingRecords.unshift(record);
      common_vendor.index.setStorageSync("foodAnalysisRecords", existingRecords);
      historyRecords.value = existingRecords;
      common_vendor.index.showToast({
        title: "保存成功",
        icon: "success"
      });
    };
    const loadHistoryRecords = () => {
      const records = common_vendor.index.getStorageSync("foodAnalysisRecords") || [];
      historyRecords.value = records;
    };
    const saveHistoryRecord = (record) => {
      try {
        const records = common_vendor.index.getStorageSync("foodAnalysisRecords") || [];
        records.unshift(record);
        if (records.length > 50) {
          records.splice(50);
        }
        common_vendor.index.setStorageSync("foodAnalysisRecords", records);
        historyRecords.value = records;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/blood-glucose/food-analysis.vue:705", "保存历史记录失败:", error);
      }
    };
    const formatDate = (timestamp) => {
      const date = new Date(timestamp);
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      return `${month}月${day}日 ${hours}:${minutes}`;
    };
    const viewHistoryDetail = (record) => {
      analysisResult.value = record;
      selectedImage.value = record.image;
    };
    const viewAllHistory = () => {
      common_vendor.index.navigateTo({
        url: "/pages/blood-glucose/food-history"
      });
    };
    common_vendor.onMounted(() => {
      loadHistoryRecords();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          isHomePage: false,
          title: "饮食分析"
        }),
        b: selectedImage.value
      }, selectedImage.value ? {
        c: selectedImage.value
      } : {
        d: common_assets._imports_0$8
      }, {
        e: common_vendor.o(chooseImage),
        f: selectedImage.value
      }, selectedImage.value ? {
        g: common_vendor.o(reSelectImage),
        h: common_vendor.t(analyzing.value ? "分析中..." : "开始分析"),
        i: common_vendor.o(analyzeFood),
        j: analyzing.value
      } : {}, {
        k: analyzing.value || errorMessage.value
      }, analyzing.value || errorMessage.value ? common_vendor.e({
        l: analyzing.value
      }, analyzing.value ? {
        m: common_vendor.t(currentStep.value)
      } : {}, {
        n: errorMessage.value
      }, errorMessage.value ? {
        o: common_vendor.t(errorMessage.value)
      } : {}) : {}, {
        p: analysisResult.value
      }, analysisResult.value ? common_vendor.e({
        q: common_vendor.t(analysisResult.value.foodName),
        r: common_vendor.t(analysisResult.value.confidence),
        s: common_vendor.t(analysisResult.value.nutrition.calories),
        t: common_vendor.t(analysisResult.value.nutrition.carbs),
        v: common_vendor.t(analysisResult.value.nutrition.protein),
        w: common_vendor.t(analysisResult.value.nutrition.fat),
        x: common_vendor.t(analysisResult.value.nutrition.fiber),
        y: common_vendor.t(analysisResult.value.nutrition.sugar),
        z: common_vendor.t(analysisResult.value.giValue),
        A: common_vendor.n(getGIClass(analysisResult.value.giValue)),
        B: common_vendor.t(getGIDescription(analysisResult.value.giValue)),
        C: analysisResult.value.detailedAnalysis
      }, analysisResult.value.detailedAnalysis ? {
        D: common_assets._imports_1$8,
        E: common_vendor.t(analysisResult.value.healthAdvice.join("；")),
        F: common_vendor.t(analysisResult.value.detailedAnalysis.canEat.join("；")),
        G: common_vendor.t(analysisResult.value.detailedAnalysis.limitEat.join("；")),
        H: common_vendor.t(analysisResult.value.detailedAnalysis.suggestions.join("；"))
      } : {}, {
        I: common_vendor.o(saveAnalysisRecord)
      }) : {}, {
        J: historyRecords.value.length > 0
      }, historyRecords.value.length > 0 ? {
        K: common_vendor.o(viewAllHistory),
        L: common_vendor.f(historyRecords.value.slice(0, 3), (record, k0, i0) => {
          return {
            a: record.image,
            b: common_vendor.t(record.foodName),
            c: common_vendor.t(formatDate(record.timestamp)),
            d: common_vendor.t(record.giValue),
            e: common_vendor.n(getGIClass(record.giValue)),
            f: record.id,
            g: common_vendor.o(($event) => viewHistoryDetail(record), record.id)
          };
        })
      } : {}, {
        M: common_vendor.s(contentStyle.value)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8e60890c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/blood-glucose/food-analysis.js.map
