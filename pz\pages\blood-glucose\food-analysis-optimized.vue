<template>
	<view class="page-container">
		<navbar :isHomePage="false" title="饮食分析" />

		<!-- 主体内容 -->
		<view class="content-container" :style="contentStyle">

			<!-- 上传区域 -->
			<view class="upload-section">
				<view class="upload-header">
					<text class="upload-title">AI食物识别</text>
					<text class="upload-subtitle">拍照或选择图片，AI将分析食物营养成分</text>
				</view>

				<view class="upload-area" @tap="chooseImage">
					<image v-if="selectedImage" :src="selectedImage" class="preview-image" mode="aspectFit"></image>
					<view v-else class="upload-placeholder">
						<image src="../../static/resource/images/camera.png" class="upload-icon"></image>
						<text class="upload-text">点击上传食物图片</text>
						<text class="upload-hint">支持JPG、PNG格式</text>
					</view>
				</view>

				<view class="upload-actions" v-if="selectedImage">
					<button class="action-btn secondary" @tap="reSelectImage">重新选择</button>
					<button class="action-btn primary" @tap="analyzeFood" :disabled="analyzing">
						{{ analyzing ? '分析中...' : '开始分析' }}
					</button>
				</view>

				<!-- 分析状态提示 -->
				<view class="analysis-status" v-if="analyzing || errorMessage">
					<view class="status-item" v-if="analyzing">
						<view class="loading-icon"></view>
						<text class="status-text">{{ currentStep }}</text>
					</view>
					<view class="error-item" v-if="errorMessage">
						<text class="error-icon">⚠️</text>
						<text class="error-text">{{ errorMessage }}</text>
					</view>
				</view>
			</view>

			<!-- 识别结果展示 -->
			<view class="recognition-results" v-if="recognitionResults && (recognitionResults.dishResults.length > 0 || recognitionResults.imageUnderstanding.length > 0)">
				<view class="result-header">
					<text class="result-title">识别结果</text>
				</view>

				<!-- 菜品识别结果 -->
				<view class="dish-results" v-if="recognitionResults.dishResults.length > 0">
					<view class="section-title">🍽️ 菜品识别结果</view>
					<view class="dish-list">
						<view class="dish-item" v-for="(dish, index) in recognitionResults.dishResults" :key="index">
							<view class="dish-info">
								<text class="dish-name">{{ dish.name }}</text>
								<text class="dish-confidence">置信度: {{ Math.round(dish.confidence * 100) }}%</text>
							</view>
							<view class="dish-details" v-if="dish.calorie">
								<text class="dish-calorie">热量: {{ dish.calorie }}卡/100g</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 图像理解结果 -->
				<view class="understanding-results" v-if="recognitionResults.imageUnderstanding.length > 0">
					<view class="section-title">🔍 图像内容理解</view>
					<view class="understanding-content">
						<view class="keywords" v-if="recognitionResults.allKeywords.length > 0">
							<text class="label">关键词:</text>
							<view class="tag-list">
								<text class="tag" v-for="keyword in recognitionResults.allKeywords.slice(0, 8)" :key="keyword">{{ keyword }}</text>
							</view>
						</view>
						<view class="tags" v-if="recognitionResults.allTags.length > 0">
							<text class="label">标签:</text>
							<view class="tag-list">
								<text class="tag" v-for="tag in recognitionResults.allTags.slice(0, 8)" :key="tag">{{ tag }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 综合分析按钮 -->
				<view class="analyze-actions">
					<button class="action-btn primary full-width" @tap="performNutritionAnalysis" :disabled="nutritionAnalyzing">
						{{ nutritionAnalyzing ? '正在分析营养成分...' : '开始营养分析' }}
					</button>
				</view>
			</view>

			<!-- 营养分析结果 -->
			<view class="analysis-result" v-if="analysisResult">
				<view class="result-header">
					<text class="result-title">营养分析结果</text>
				</view>

				<!-- 识别的食物 -->
				<view class="food-info">
					<view class="food-name">
						<text class="name-text">{{ analysisResult.foodName }}</text>
						<view class="confidence-badge">
							<text class="confidence-text">综合置信度: {{ analysisResult.confidence }}%</text>
						</view>
					</view>
					
					<!-- API使用信息 -->
					<view class="api-info" v-if="analysisResult.analysisSource">
						<view class="api-methods">
							<text class="api-label">识别方法:</text>
							<text class="api-value">{{ analysisResult.analysisSource.methods?.join(' + ') || '智能识别' }}</text>
						</view>
						<view class="api-quality">
							<text class="quality-label">数据质量:</text>
							<text class="quality-value" :class="'quality-' + (analysisResult.analysisSource.confidence_level || 'medium')">
								{{ getQualityText(analysisResult.analysisSource.confidence_level) }}
							</text>
						</view>
					</view>
				</view>

				<!-- 营养成分 -->
				<view class="nutrition-section">
					<view class="section-title">营养成分 (每100g)</view>
					<view class="nutrition-grid">
						<view class="nutrition-item">
							<text class="nutrition-label">热量</text>
							<text class="nutrition-value">{{ analysisResult.nutrition.calories }}</text>
							<text class="nutrition-unit">kcal</text>
						</view>
						<view class="nutrition-item">
							<text class="nutrition-label">碳水化合物</text>
							<text class="nutrition-value">{{ analysisResult.nutrition.carbs }}</text>
							<text class="nutrition-unit">g</text>
						</view>
						<view class="nutrition-item">
							<text class="nutrition-label">蛋白质</text>
							<text class="nutrition-value">{{ analysisResult.nutrition.protein }}</text>
							<text class="nutrition-unit">g</text>
						</view>
						<view class="nutrition-item">
							<text class="nutrition-label">脂肪</text>
							<text class="nutrition-value">{{ analysisResult.nutrition.fat }}</text>
							<text class="nutrition-unit">g</text>
						</view>
						<view class="nutrition-item">
							<text class="nutrition-label">膳食纤维</text>
							<text class="nutrition-value">{{ analysisResult.nutrition.fiber }}</text>
							<text class="nutrition-unit">g</text>
						</view>
						<view class="nutrition-item">
							<text class="nutrition-label">糖分</text>
							<text class="nutrition-value">{{ analysisResult.nutrition.sugar }}</text>
							<text class="nutrition-unit">g</text>
						</view>
					</view>
				</view>

				<!-- GI值 -->
				<view class="gi-section">
					<view class="section-title">血糖生成指数 (GI值)</view>
					<view class="gi-display">
						<view class="gi-value" :class="getGIClass(analysisResult.giValue)">
							<text class="gi-number">{{ analysisResult.giValue }}</text>
						</view>
						<view class="gi-info">
							<text class="gi-level">{{ getGIClass(analysisResult.giValue) === 'low' ? '低GI' : getGIClass(analysisResult.giValue) === 'medium' ? '中GI' : '高GI' }}</text>
							<text class="gi-description">{{ getGIDescription(analysisResult.giValue) }}</text>
						</view>
					</view>
				</view>

				<!-- 健康建议 -->
				<view class="advice-section">
					<view class="section-title">健康建议</view>
					<view class="advice-list">
						<view class="advice-item" v-for="(advice, index) in analysisResult.healthAdvice" :key="index">
							<text class="advice-icon">💡</text>
							<text class="advice-text">{{ advice }}</text>
						</view>
					</view>
				</view>

				<!-- 详细分析 -->
				<view class="detailed-analysis" v-if="analysisResult.detailedAnalysis">
					<view class="section-title">详细分析</view>
					
					<view class="analysis-group" v-if="analysisResult.detailedAnalysis.canEat?.length">
						<view class="group-title can-eat">✅ 推荐食用</view>
						<view class="analysis-item" v-for="(item, index) in analysisResult.detailedAnalysis.canEat" :key="index">
							<text class="analysis-text">{{ item }}</text>
						</view>
					</view>

					<view class="analysis-group" v-if="analysisResult.detailedAnalysis.limitEat?.length">
						<view class="group-title limit-eat">⚠️ 限制食用</view>
						<view class="analysis-item" v-for="(item, index) in analysisResult.detailedAnalysis.limitEat" :key="index">
							<text class="analysis-text">{{ item }}</text>
						</view>
					</view>

					<view class="analysis-group" v-if="analysisResult.detailedAnalysis.suggestions?.length">
						<view class="group-title suggestions">💡 饮食建议</view>
						<view class="analysis-item" v-for="(item, index) in analysisResult.detailedAnalysis.suggestions" :key="index">
							<text class="analysis-text">{{ item }}</text>
						</view>
					</view>
				</view>

				<!-- 操作按钮 -->
				<view class="result-actions">
					<button class="action-btn secondary" @tap="reSelectImage">重新分析</button>
					<button class="action-btn primary" @tap="saveToHistory">保存记录</button>
				</view>
			</view>

			<!-- 历史记录 -->
			<view class="history-section" v-if="historyRecords.length > 0">
				<view class="section-title">历史记录</view>
				<view class="history-list">
					<view class="history-item" v-for="record in historyRecords" :key="record.id" @tap="viewHistoryDetail(record)">
						<image :src="record.image" class="history-image" mode="aspectFill"></image>
						<view class="history-info">
							<text class="history-name">{{ record.foodName }}</text>
							<text class="history-date">{{ formatDate(record.timestamp) }}</text>
						</view>
						<view class="history-gi" :class="getGIClass(record.giValue)">
							<text class="history-gi-text">GI: {{ record.giValue }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
		onMounted
	} from 'vue'
	import navbar from '../../components/navbar/navbar.vue'
	import {
		sendMessageToAI
	} from '../../utils/ai/service'

	// 百度智能云API配置（优化版）
	const baiduConfig = {
		apiKey: '956Ds55wM8lZyOqHc9Xl6Sdj',
		secretKey: '6w7lYp7c9pQuDgOqGu0x1Dx8dDAo1HqH',
		tokenUrl: 'https://aip.baidubce.com/oauth/2.0/token',
		// 菜品识别API
		dishDetectUrl: 'https://aip.baidubce.com/rest/2.0/image-classify/v2/dish',
		// 图像内容理解API
		imageUnderstandingRequestUrl: 'https://aip.baidubce.com/rest/2.0/image-classify/v1/image-understanding/request',
		imageUnderstandingResultUrl: 'https://aip.baidubce.com/rest/2.0/image-classify/v1/image-understanding/get-result'
	}

	// Access Token缓存
	let cachedAccessToken = null
	let tokenExpireTime = 0
	
	// API调用控制机制
	let lastApiCallTime = 0
	const API_CALL_INTERVAL = 5000 // 5秒间隔
	const MAX_RETRIES = 2
	const RETRY_DELAY_BASE = 8000
	let globalApiLock = false
	
	// API策略配置（简化版）
	const API_STRATEGY = {
		DISH_CONFIDENCE_THRESHOLD: 0.4, // 降低阈值获取更多结果
		USE_IMAGE_UNDERSTANDING: true,   // 始终使用图像理解
		MAX_UNDERSTANDING_WAIT: 30000,   // 图像理解最大等待时间
		COMBINE_RESULTS: true            // 始终整合结果
	}

	/**
	 * 将图片转为 base64
	 */
	function getBase64(filePath) {
		return new Promise((resolve, reject) => {
			uni.getFileSystemManager().readFile({
				filePath,
				encoding: 'base64',
				success: res => resolve(res.data),
				fail: reject
			})
		})
	}

	/**
	 * 延迟函数
	 */
	function delay(ms) {
		return new Promise(resolve => setTimeout(resolve, ms))
	}

	/**
	 * 获取百度API Access Token
	 */
	async function getBaiduAccessToken() {
		const currentTime = Date.now()
		if (cachedAccessToken && currentTime < tokenExpireTime) {
			return cachedAccessToken
		}

		try {
			const response = await uni.request({
				url: baiduConfig.tokenUrl,
				method: 'POST',
				data: {
					grant_type: 'client_credentials',
					client_id: baiduConfig.apiKey,
					client_secret: baiduConfig.secretKey
				},
				header: {
					'Content-Type': 'application/x-www-form-urlencoded'
				}
			})

			if (response.statusCode === 200 && response.data.access_token) {
				cachedAccessToken = response.data.access_token
				tokenExpireTime = currentTime + (response.data.expires_in - 300) * 1000
				console.log('✅ 百度Access Token获取成功')
				return cachedAccessToken
			} else {
				throw new Error('获取Access Token失败: ' + JSON.stringify(response.data))
			}
		} catch (error) {
			console.error('❌ 获取百度Access Token失败:', error)
			throw new Error('百度API认证失败')
		}
	}

	/**
	 * 百度菜品识别API调用
	 */
	async function callDishDetectionAPI(filePath, retryCount = 0) {
		try {
			console.log('🍽️ 调用百度菜品识别API...')
			const accessToken = await getBaiduAccessToken()
			const base64 = await getBase64(filePath)

			const response = await uni.request({
				url: `${baiduConfig.dishDetectUrl}?access_token=${accessToken}`,
				method: 'POST',
				data: {
					image: base64,
					top_num: 6, // 增加返回结果数量
					filter_threshold: 0.3, // 降低过滤阈值
					baike_num: 3
				},
				header: {
					'Content-Type': 'application/x-www-form-urlencoded'
				},
				timeout: 15000
			})

			if (response.statusCode === 200 && response.data) {
				const result = response.data

				if (result.error_code) {
					if (result.error_code === 18 && retryCount < MAX_RETRIES) {
						const retryDelay = RETRY_DELAY_BASE * (retryCount + 1)
						console.log(`⏳ 菜品识别QPS限制，等待${retryDelay/1000}秒后重试...`)
						await delay(retryDelay)
						return await callDishDetectionAPI(filePath, retryCount + 1)
					}
					throw new Error(`菜品识别API错误: ${result.error_msg} (错误码: ${result.error_code})`)
				}

				if (result.result && result.result.length > 0) {
					console.log('✅ 菜品识别成功，识别到', result.result.length, '个结果')
					return {
						success: true,
						data: result.result.map(item => ({
							name: item.name,
							confidence: item.probability,
							calorie: item.calorie || null,
							baike_info: item.baike_info || null
						}))
					}
				} else {
					console.log('⚠️ 菜品识别未找到结果')
					return {
						success: false,
						data: [],
						message: '未识别到菜品'
					}
				}
			} else {
				throw new Error(`菜品识别API调用失败: HTTP ${response.statusCode}`)
			}
		} catch (error) {
			console.error('❌ 菜品识别API失败:', error)
			return {
				success: false,
				data: [],
				error: error.message
			}
		}
	}

	/**
	 * 百度图像内容理解API调用
	 */
	async function callImageUnderstandingAPI(filePath, retryCount = 0) {
		try {
			console.log('🔍 调用百度图像内容理解API...')
			const accessToken = await getBaiduAccessToken()
			const base64 = await getBase64(filePath)

			// 第一步：提交图像理解请求
			const requestResponse = await uni.request({
				url: `${baiduConfig.imageUnderstandingRequestUrl}?access_token=${accessToken}`,
				method: 'POST',
				data: {
					image: base64,
					scenes: ['food', 'general'], // 专注于食物和通用场景
					feature: ['keyword', 'tag']  // 获取关键词和标签
				},
				header: {
					'Content-Type': 'application/x-www-form-urlencoded'
				},
				timeout: 15000
			})

			if (requestResponse.statusCode !== 200 || !requestResponse.data) {
				throw new Error(`图像理解请求失败: HTTP ${requestResponse.statusCode}`)
			}

			const requestResult = requestResponse.data
			if (requestResult.error_code) {
				if (requestResult.error_code === 18 && retryCount < MAX_RETRIES) {
					const retryDelay = RETRY_DELAY_BASE * (retryCount + 1)
					console.log(`⏳ 图像理解QPS限制，等待${retryDelay/1000}秒后重试...`)
					await delay(retryDelay)
					return await callImageUnderstandingAPI(filePath, retryCount + 1)
				}
				throw new Error(`图像理解请求错误: ${requestResult.error_msg} (错误码: ${requestResult.error_code})`)
			}

			if (!requestResult.task_id) {
				throw new Error('图像理解请求失败：未获取到任务ID')
			}

			const taskId = requestResult.task_id
			console.log(`📋 图像理解任务ID: ${taskId}`)

			// 第二步：轮询获取结果
			let attempts = 0
			const maxAttempts = 12 // 增加尝试次数
			const pollInterval = 2500 // 缩短轮询间隔

			while (attempts < maxAttempts) {
				await delay(pollInterval)
				attempts++

				console.log(`🔄 获取图像理解结果，第${attempts}次尝试...`)
				const resultResponse = await uni.request({
					url: `${baiduConfig.imageUnderstandingResultUrl}?access_token=${accessToken}`,
					method: 'POST',
					data: {
						task_id: taskId
					},
					header: {
						'Content-Type': 'application/x-www-form-urlencoded'
					},
					timeout: 10000
				})

				if (resultResponse.statusCode === 200 && resultResponse.data) {
					const result = resultResponse.data

					if (result.error_code) {
						throw new Error(`图像理解结果错误: ${result.error_msg} (错误码: ${result.error_code})`)
					}

					if (result.task_status === 2) {
						console.log('✅ 图像内容理解完成')
						return {
							success: true,
							data: result.results || []
						}
					} else if (result.task_status === 3) {
						throw new Error('图像理解任务失败')
					}
					// task_status === 1 表示处理中，继续轮询
				}
			}

			console.log('⚠️ 图像理解超时，但不影响整体流程')
			return {
				success: false,
				data: [],
				message: '图像理解超时'
			}
		} catch (error) {
			console.error('❌ 图像内容理解API失败:', error)
			return {
				success: false,
				data: [],
				error: error.message
			}
		}
	}

	// 响应式数据
	const selectedImage = ref('')
	const analyzing = ref(false)
	const nutritionAnalyzing = ref(false)
	const analysisResult = ref(null)
	const recognitionResults = ref(null) // 新增：存储识别结果
	const historyRecords = ref([])
	const currentStep = ref('') // 当前处理步骤
	const errorMessage = ref('') // 错误信息

	// 辅助函数
	const getQualityText = (level) => {
		switch (level) {
			case 'high':
				return '高质量'
			case 'medium':
				return '中等质量'
			case 'low':
				return '基础质量'
			default:
				return '标准质量'
		}
	}

	// 计算属性
	const contentStyle = computed(() => {
		const menuButtonInfo = uni.getMenuButtonBoundingClientRect()
		const navHeight = menuButtonInfo.top + menuButtonInfo.height + 8
		return {
			paddingTop: navHeight + 'px'
		}
	})

	// 获取GI值等级样式类
	const getGIClass = (giValue) => {
		if (giValue <= 55) return 'low'
		if (giValue <= 70) return 'medium'
		return 'high'
	}

	// 获取GI值描述
	const getGIDescription = (giValue) => {
		if (giValue <= 55) return '低GI食物，对血糖影响较小，适合糖尿病患者食用'
		if (giValue <= 70) return '中等GI食物，适量食用，建议搭配低GI食物'
		return '高GI食物，会快速升高血糖，糖尿病患者需严格控制'
	}

	// 方法
	const chooseImage = () => {
		uni.chooseImage({
			count: 1,
			sizeType: ['compressed'],
			sourceType: ['camera', 'album'],
			success: (res) => {
				selectedImage.value = res.tempFilePaths[0]
				analysisResult.value = null
				recognitionResults.value = null
				errorMessage.value = ''
			}
		})
	}

	const reSelectImage = () => {
		selectedImage.value = ''
		analysisResult.value = null
		recognitionResults.value = null
		errorMessage.value = ''
	}
