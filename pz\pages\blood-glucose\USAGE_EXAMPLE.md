# 食物识别模块使用示例

## 基本使用流程

### 1. 在Vue组件中使用

```vue
<template>
  <view class="food-analysis">
    <!-- 图片上传区域 -->
    <view class="upload-section">
      <button @tap="chooseImage">选择食物图片</button>
      <image v-if="selectedImage" :src="selectedImage" />
    </view>
    
    <!-- 识别按钮 -->
    <button @tap="analyzeFood" :disabled="analyzing">
      {{ analyzing ? '识别中...' : '开始识别' }}
    </button>
    
    <!-- 识别结果 -->
    <view v-if="recognitionResults" class="results">
      <!-- 菜品识别结果 -->
      <view v-if="recognitionResults.dishResults.length > 0">
        <text>菜品识别结果：</text>
        <view v-for="dish in recognitionResults.dishResults" :key="dish.name">
          {{ dish.name }} ({{ Math.round(dish.confidence * 100) }}%)
        </view>
      </view>
      
      <!-- 果蔬识别结果 -->
      <view v-if="recognitionResults.ingredientResults.length > 0">
        <text>果蔬识别结果：</text>
        <view v-for="ingredient in recognitionResults.ingredientResults" :key="ingredient.name">
          {{ ingredient.name }} ({{ Math.round(ingredient.confidence * 100) }}%)
        </view>
      </view>
    </view>
    
    <!-- 营养分析按钮 -->
    <button v-if="recognitionResults" @tap="performNutritionAnalysis" :disabled="nutritionAnalyzing">
      {{ nutritionAnalyzing ? '分析中...' : '开始营养分析' }}
    </button>
    
    <!-- 营养分析结果 -->
    <view v-if="analysisResult" class="nutrition-result">
      <text>{{ analysisResult.foodName }}</text>
      <text>热量: {{ analysisResult.nutrition.calories }}kcal</text>
      <text>GI值: {{ analysisResult.giValue }}</text>
      <!-- 更多营养信息... -->
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { comprehensiveFoodRecognition } from '../../utils/ai/baiduImageService'
import { sendMessageToAI } from '../../utils/ai/service'

const selectedImage = ref('')
const analyzing = ref(false)
const nutritionAnalyzing = ref(false)
const recognitionResults = ref(null)
const analysisResult = ref(null)

// 选择图片
const chooseImage = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      selectedImage.value = res.tempFilePaths[0]
      recognitionResults.value = null
      analysisResult.value = null
    }
  })
}

// 分析食物
const analyzeFood = async () => {
  if (!selectedImage.value) return
  
  analyzing.value = true
  try {
    const result = await comprehensiveFoodRecognition(selectedImage.value)
    if (result.success) {
      recognitionResults.value = result.data
    }
  } catch (error) {
    console.error('识别失败:', error)
  } finally {
    analyzing.value = false
  }
}

// 营养分析
const performNutritionAnalysis = async () => {
  if (!recognitionResults.value) return
  
  nutritionAnalyzing.value = true
  try {
    const prompt = `分析以下食物的营养成分：${recognitionResults.value.summary}`
    const aiResponse = await sendMessageToAI(prompt)
    // 解析AI响应...
    analysisResult.value = JSON.parse(aiResponse)
  } catch (error) {
    console.error('营养分析失败:', error)
  } finally {
    nutritionAnalyzing.value = false
  }
}
</script>
```

### 2. 直接使用服务函数

```javascript
import { 
  comprehensiveFoodRecognition,
  callDishDetectionAPI,
  callIngredientDetectionAPI 
} from '../../utils/ai/baiduImageService'

// 综合识别
async function recognizeFood(imagePath) {
  try {
    const result = await comprehensiveFoodRecognition(imagePath)
    
    if (result.success) {
      console.log('识别成功:', result.data)
      
      // 访问不同类型的识别结果
      const dishes = result.data.dishResults
      const ingredients = result.data.ingredientResults
      const keywords = result.data.allKeywords
      const summary = result.data.summary
      
      return result.data
    } else {
      console.error('识别失败:', result.error)
      return null
    }
  } catch (error) {
    console.error('识别异常:', error)
    return null
  }
}

// 单独调用菜品识别
async function recognizeDish(imagePath) {
  try {
    const result = await callDishDetectionAPI(imagePath)
    
    if (result.success) {
      result.data.forEach(dish => {
        console.log(`菜品: ${dish.name}, 置信度: ${dish.confidence}`)
      })
    }
    
    return result
  } catch (error) {
    console.error('菜品识别失败:', error)
    return null
  }
}
```

## 高级用法

### 1. 自定义错误处理

```javascript
import { comprehensiveFoodRecognition } from '../../utils/ai/baiduImageService'

async function recognizeWithCustomErrorHandling(imagePath) {
  try {
    const result = await comprehensiveFoodRecognition(imagePath)
    
    if (result.success) {
      return result.data
    } else {
      // 根据错误类型进行不同处理
      if (result.error.includes('网络')) {
        uni.showToast({ title: '网络连接失败，请检查网络' })
      } else if (result.error.includes('QPS')) {
        uni.showToast({ title: '请求过于频繁，请稍后重试' })
      } else {
        uni.showToast({ title: '识别失败，请重试' })
      }
      return null
    }
  } catch (error) {
    console.error('识别异常:', error)
    uni.showToast({ title: '系统异常，请稍后重试' })
    return null
  }
}
```

### 2. 批量识别

```javascript
async function batchRecognition(imagePaths) {
  const results = []
  
  for (const imagePath of imagePaths) {
    try {
      const result = await comprehensiveFoodRecognition(imagePath)
      results.push({
        imagePath,
        success: result.success,
        data: result.success ? result.data : null,
        error: result.success ? null : result.error
      })
      
      // 添加延迟避免QPS限制
      await new Promise(resolve => setTimeout(resolve, 1000))
    } catch (error) {
      results.push({
        imagePath,
        success: false,
        data: null,
        error: error.message
      })
    }
  }
  
  return results
}
```

### 3. 结果缓存

```javascript
const recognitionCache = new Map()

async function recognizeWithCache(imagePath) {
  // 生成缓存键（可以使用图片的hash值）
  const cacheKey = imagePath
  
  // 检查缓存
  if (recognitionCache.has(cacheKey)) {
    console.log('使用缓存结果')
    return recognitionCache.get(cacheKey)
  }
  
  // 执行识别
  const result = await comprehensiveFoodRecognition(imagePath)
  
  // 缓存成功结果
  if (result.success) {
    recognitionCache.set(cacheKey, result.data)
    
    // 限制缓存大小
    if (recognitionCache.size > 50) {
      const firstKey = recognitionCache.keys().next().value
      recognitionCache.delete(firstKey)
    }
  }
  
  return result.success ? result.data : null
}
```

## 测试和调试

### 1. 运行测试

```javascript
import { runAllTests, performanceTest } from '../../utils/ai/baiduImageService.test'

// 运行完整测试套件
async function testBaiduServices() {
  const testImagePath = '/path/to/test/image.jpg'
  const results = await runAllTests(testImagePath)
  
  console.log('测试结果:', results)
}

// 性能测试
async function testPerformance() {
  const testImagePath = '/path/to/test/image.jpg'
  const stats = await performanceTest(testImagePath, 5)
  
  console.log('性能统计:', stats)
}
```

### 2. 调试技巧

```javascript
// 启用详细日志
const DEBUG = true

async function debugRecognition(imagePath) {
  if (DEBUG) {
    console.log('开始识别:', imagePath)
  }
  
  const result = await comprehensiveFoodRecognition(imagePath)
  
  if (DEBUG) {
    console.log('识别结果:', JSON.stringify(result, null, 2))
  }
  
  return result
}

// 监控API调用
let apiCallCount = 0

function trackApiCall(apiName) {
  apiCallCount++
  console.log(`API调用 #${apiCallCount}: ${apiName}`)
}
```

## 最佳实践

### 1. 图片质量优化

```javascript
// 图片预处理
function preprocessImage(imagePath) {
  return new Promise((resolve, reject) => {
    // 压缩图片
    uni.compressImage({
      src: imagePath,
      quality: 80,
      success: (res) => {
        resolve(res.tempFilePath)
      },
      fail: reject
    })
  })
}

// 使用预处理后的图片
async function recognizeWithPreprocessing(originalImagePath) {
  try {
    const processedImagePath = await preprocessImage(originalImagePath)
    return await comprehensiveFoodRecognition(processedImagePath)
  } catch (error) {
    console.error('图片预处理失败:', error)
    // 使用原图片作为备选
    return await comprehensiveFoodRecognition(originalImagePath)
  }
}
```

### 2. 用户体验优化

```javascript
// 渐进式结果展示
async function progressiveRecognition(imagePath, onProgress) {
  const results = {
    dishResults: [],
    ingredientResults: [],
    allKeywords: [],
    allTags: []
  }
  
  // 先显示菜品识别结果
  try {
    const dishResult = await callDishDetectionAPI(imagePath)
    if (dishResult.success) {
      results.dishResults = dishResult.data
      onProgress('dish', results)
    }
  } catch (error) {
    console.error('菜品识别失败:', error)
  }
  
  // 再显示果蔬识别结果
  try {
    const ingredientResult = await callIngredientDetectionAPI(imagePath)
    if (ingredientResult.success) {
      results.ingredientResults = ingredientResult.data
      onProgress('ingredient', results)
    }
  } catch (error) {
    console.error('果蔬识别失败:', error)
  }
  
  // 最后显示图像理解结果
  try {
    const understandingResult = await callImageUnderstandingAPI(imagePath)
    if (understandingResult.success) {
      // 处理关键词和标签...
      onProgress('understanding', results)
    }
  } catch (error) {
    console.error('图像理解失败:', error)
  }
  
  return results
}
```

### 3. 错误恢复

```javascript
// 智能重试机制
async function recognizeWithRetry(imagePath, maxRetries = 3) {
  let lastError = null
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await comprehensiveFoodRecognition(imagePath)
      
      if (result.success) {
        return result
      } else {
        lastError = new Error(result.error)
      }
    } catch (error) {
      lastError = error
      
      if (attempt < maxRetries) {
        // 指数退避
        const delay = Math.pow(2, attempt) * 1000
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
  }
  
  throw lastError
}
```
