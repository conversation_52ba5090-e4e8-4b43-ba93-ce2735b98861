// 百度AI图像识别服务
// 集成菜品识别、果蔬识别、图像内容理解三个API

// 百度AI配置
export const BAIDU_CONFIG = {
  apiKey: '956Ds55wM8lZyOqHc9Xl6Sdj',
  secretKey: '6w7lYp7c9pQuDgOqGu0x1Dx8dDAo1HqH',
  tokenUrl: 'https://aip.baidubce.com/oauth/2.0/token',
  // 菜品识别API
  dishDetectUrl: 'https://aip.baidubce.com/rest/2.0/image-classify/v2/dish',
  // 果蔬识别API
  ingredientUrl: 'https://aip.baidubce.com/rest/2.0/image-classify/v1/classify/ingredient',
  // 图像内容理解API
  imageUnderstandingRequestUrl: 'https://aip.baidubce.com/rest/2.0/image-classify/v1/image-understanding/request',
  imageUnderstandingResultUrl: 'https://aip.baidubce.com/rest/2.0/image-classify/v1/image-understanding/get-result'
}

// Access Token缓存
let cachedAccessToken = null
let tokenExpireTime = 0

// API调用控制
const MAX_RETRIES = 2
const RETRY_DELAY_BASE = 8000

/**
 * 将图片转为 base64
 */
function getBase64(filePath) {
  return new Promise((resolve, reject) => {
    uni.getFileSystemManager().readFile({
      filePath,
      encoding: 'base64',
      success: res => resolve(res.data),
      fail: reject
    })
  })
}

/**
 * 延迟函数
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 获取百度API Access Token
 */
export async function getBaiduAccessToken() {
  const currentTime = Date.now()
  if (cachedAccessToken && currentTime < tokenExpireTime) {
    return cachedAccessToken
  }

  try {
    const response = await uni.request({
      url: BAIDU_CONFIG.tokenUrl,
      method: 'POST',
      data: {
        grant_type: 'client_credentials',
        client_id: BAIDU_CONFIG.apiKey,
        client_secret: BAIDU_CONFIG.secretKey
      },
      header: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })

    if (response.statusCode === 200 && response.data.access_token) {
      cachedAccessToken = response.data.access_token
      tokenExpireTime = currentTime + (response.data.expires_in - 300) * 1000
      console.log('✅ 百度Access Token获取成功')
      return cachedAccessToken
    } else {
      throw new Error('获取Access Token失败: ' + JSON.stringify(response.data))
    }
  } catch (error) {
    console.error('❌ 获取百度Access Token失败:', error)
    throw new Error('百度API认证失败')
  }
}

/**
 * 百度菜品识别API调用
 */
export async function callDishDetectionAPI(filePath, retryCount = 0) {
  try {
    console.log('🍽️ 调用百度菜品识别API...')
    const accessToken = await getBaiduAccessToken()
    const base64 = await getBase64(filePath)

    const response = await uni.request({
      url: `${BAIDU_CONFIG.dishDetectUrl}?access_token=${accessToken}`,
      method: 'POST',
      data: {
        image: base64,
        top_num: 6, // 增加返回结果数量
        filter_threshold: 0.3, // 降低过滤阈值
        baike_num: 3
      },
      header: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      timeout: 15000
    })

    if (response.statusCode === 200 && response.data) {
      const result = response.data

      if (result.error_code) {
        if (result.error_code === 18 && retryCount < MAX_RETRIES) {
          const retryDelay = RETRY_DELAY_BASE * (retryCount + 1)
          console.log(`⏳ 菜品识别QPS限制，等待${retryDelay/1000}秒后重试...`)
          await delay(retryDelay)
          return await callDishDetectionAPI(filePath, retryCount + 1)
        }
        throw new Error(`菜品识别API错误: ${result.error_msg} (错误码: ${result.error_code})`)
      }

      if (result.result && result.result.length > 0) {
        console.log('✅ 菜品识别成功，识别到', result.result.length, '个结果')
        return {
          success: true,
          data: result.result.map(item => ({
            name: item.name,
            confidence: item.probability,
            calorie: item.calorie || null,
            baike_info: item.baike_info || null
          }))
        }
      } else {
        console.log('⚠️ 菜品识别未找到结果')
        return {
          success: false,
          data: [],
          message: '未识别到菜品'
        }
      }
    } else {
      throw new Error(`菜品识别API调用失败: HTTP ${response.statusCode}`)
    }
  } catch (error) {
    console.error('❌ 菜品识别API失败:', error)
    return {
      success: false,
      data: [],
      error: error.message
    }
  }
}

/**
 * 百度果蔬识别API调用
 */
export async function callIngredientDetectionAPI(filePath, retryCount = 0) {
  try {
    console.log('🥬 调用百度果蔬识别API...')
    const accessToken = await getBaiduAccessToken()
    const base64 = await getBase64(filePath)

    const response = await uni.request({
      url: `${BAIDU_CONFIG.ingredientUrl}?access_token=${accessToken}`,
      method: 'POST',
      data: {
        image: base64,
        top_num: 5 // 返回前5个识别结果
      },
      header: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      timeout: 15000
    })

    if (response.statusCode === 200 && response.data) {
      const result = response.data

      if (result.error_code) {
        if (result.error_code === 18 && retryCount < MAX_RETRIES) {
          const retryDelay = RETRY_DELAY_BASE * (retryCount + 1)
          console.log(`⏳ 果蔬识别QPS限制，等待${retryDelay/1000}秒后重试...`)
          await delay(retryDelay)
          return await callIngredientDetectionAPI(filePath, retryCount + 1)
        }
        throw new Error(`果蔬识别API错误: ${result.error_msg} (错误码: ${result.error_code})`)
      }

      if (result.result && result.result.length > 0) {
        console.log('✅ 果蔬识别成功，识别到', result.result.length, '个结果')
        return {
          success: true,
          data: result.result.map(item => ({
            name: item.name,
            confidence: item.score,
            category: '果蔬类'
          }))
        }
      } else {
        console.log('⚠️ 果蔬识别未找到结果')
        return {
          success: false,
          data: [],
          message: '未识别到果蔬'
        }
      }
    } else {
      throw new Error(`果蔬识别API调用失败: HTTP ${response.statusCode}`)
    }
  } catch (error) {
    console.error('❌ 果蔬识别API失败:', error)
    return {
      success: false,
      data: [],
      error: error.message
    }
  }
}

/**
 * 百度图像内容理解API调用
 */
export async function callImageUnderstandingAPI(filePath, retryCount = 0) {
  try {
    console.log('🔍 调用百度图像内容理解API...')
    const accessToken = await getBaiduAccessToken()
    const base64 = await getBase64(filePath)

    // 第一步：提交图像理解请求
    const requestResponse = await uni.request({
      url: `${BAIDU_CONFIG.imageUnderstandingRequestUrl}?access_token=${accessToken}`,
      method: 'POST',
      data: {
        image: base64,
        scenes: ['food', 'general'], // 专注于食物和通用场景
        feature: ['keyword', 'tag']  // 获取关键词和标签
      },
      header: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      timeout: 15000
    })

    if (requestResponse.statusCode !== 200 || !requestResponse.data) {
      throw new Error(`图像理解请求失败: HTTP ${requestResponse.statusCode}`)
    }

    const requestResult = requestResponse.data
    if (requestResult.error_code) {
      if (requestResult.error_code === 18 && retryCount < MAX_RETRIES) {
        const retryDelay = RETRY_DELAY_BASE * (retryCount + 1)
        console.log(`⏳ 图像理解QPS限制，等待${retryDelay/1000}秒后重试...`)
        await delay(retryDelay)
        return await callImageUnderstandingAPI(filePath, retryCount + 1)
      }
      throw new Error(`图像理解请求错误: ${requestResult.error_msg} (错误码: ${requestResult.error_code})`)
    }

    if (!requestResult.task_id) {
      throw new Error('图像理解请求失败：未获取到任务ID')
    }

    const taskId = requestResult.task_id
    console.log(`📋 图像理解任务ID: ${taskId}`)

    // 第二步：轮询获取结果
    let attempts = 0
    const maxAttempts = 12 // 增加尝试次数
    const pollInterval = 2500 // 缩短轮询间隔

    while (attempts < maxAttempts) {
      await delay(pollInterval)
      attempts++

      console.log(`🔄 获取图像理解结果，第${attempts}次尝试...`)
      const resultResponse = await uni.request({
        url: `${BAIDU_CONFIG.imageUnderstandingResultUrl}?access_token=${accessToken}`,
        method: 'POST',
        data: {
          task_id: taskId
        },
        header: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        timeout: 10000
      })

      if (resultResponse.statusCode === 200 && resultResponse.data) {
        const result = resultResponse.data

        if (result.error_code) {
          throw new Error(`图像理解结果错误: ${result.error_msg} (错误码: ${result.error_code})`)
        }

        if (result.task_status === 2) {
          console.log('✅ 图像内容理解完成')
          return {
            success: true,
            data: result.results || []
          }
        } else if (result.task_status === 3) {
          throw new Error('图像理解任务失败')
        }
        // task_status === 1 表示处理中，继续轮询
      }
    }

    console.log('⚠️ 图像理解超时，但不影响整体流程')
    return {
      success: false,
      data: [],
      message: '图像理解超时'
    }
  } catch (error) {
    console.error('❌ 图像内容理解API失败:', error)
    return {
      success: false,
      data: [],
      error: error.message
    }
  }
}

/**
 * 综合调用三个百度AI API进行食物识别
 */
export async function comprehensiveFoodRecognition(filePath) {
  console.log('🚀 开始综合食物识别...')
  
  try {
    // 并行调用三个API以提高效率
    const [dishResult, ingredientResult, understandingResult] = await Promise.allSettled([
      callDishDetectionAPI(filePath),
      callIngredientDetectionAPI(filePath),
      callImageUnderstandingAPI(filePath)
    ])

    const results = {
      dishResults: [],
      ingredientResults: [],
      imageUnderstanding: [],
      allKeywords: [],
      allTags: [],
      summary: ''
    }

    // 处理菜品识别结果
    if (dishResult.status === 'fulfilled' && dishResult.value.success) {
      results.dishResults = dishResult.value.data
      console.log('✅ 菜品识别完成:', dishResult.value.data.length, '个结果')
    }

    // 处理果蔬识别结果
    if (ingredientResult.status === 'fulfilled' && ingredientResult.value.success) {
      results.ingredientResults = ingredientResult.value.data
      console.log('✅ 果蔬识别完成:', ingredientResult.value.data.length, '个结果')
    }

    // 处理图像理解结果
    if (understandingResult.status === 'fulfilled' && understandingResult.value.success) {
      const understandingData = understandingResult.value.data
      results.imageUnderstanding = understandingData

      // 提取关键词和标签
      const allKeywords = []
      const allTags = []

      understandingData.forEach(item => {
        if (item.keyword && Array.isArray(item.keyword)) {
          allKeywords.push(...item.keyword)
        }
        if (item.tag && Array.isArray(item.tag)) {
          allTags.push(...item.tag)
        }
      })

      results.allKeywords = [...new Set(allKeywords)]
      results.allTags = [...new Set(allTags)]
      console.log('✅ 图像理解完成:', allKeywords.length, '个关键词,', allTags.length, '个标签')
    }

    // 生成识别摘要
    results.summary = generateRecognitionSummary(results)

    // 检查是否有任何识别结果
    const hasResults = results.dishResults.length > 0 ||
      results.ingredientResults.length > 0 ||
      results.allKeywords.length > 0

    if (!hasResults) {
      throw new Error('所有识别API都未能识别出食物，请确保图片中包含清晰的食物')
    }

    console.log('🎉 综合食物识别完成')
    return {
      success: true,
      data: results
    }

  } catch (error) {
    console.error('❌ 综合食物识别失败:', error)
    return {
      success: false,
      error: error.message || '识别过程中出现错误'
    }
  }
}

/**
 * 生成识别结果摘要
 */
function generateRecognitionSummary(results) {
  let summary = []

  // 菜品识别结果
  if (results.dishResults.length > 0) {
    const dishNames = results.dishResults
      .map(item => `${item.name}(置信度:${Math.round(item.confidence * 100)}%)`)
      .join('、')
    summary.push(`菜品识别：${dishNames}`)
  }

  // 果蔬识别结果
  if (results.ingredientResults.length > 0) {
    const ingredientNames = results.ingredientResults
      .map(item => `${item.name}(置信度:${Math.round(item.confidence * 100)}%)`)
      .join('、')
    summary.push(`果蔬识别：${ingredientNames}`)
  }

  // 图像理解关键词
  if (results.allKeywords.length > 0) {
    const keywords = results.allKeywords.slice(0, 10).join('、')
    summary.push(`图像关键词：${keywords}`)
  }

  // 图像理解标签
  if (results.allTags.length > 0) {
    const tags = results.allTags.slice(0, 8).join('、')
    summary.push(`图像标签：${tags}`)
  }

  return summary.length > 0 ? summary.join('\n') : '未能识别出明确的食物信息'
}
